
<?php
// Exit if accessed directly
if ( !defined( 'ABSPATH' ) ) exit;

add_action( 'after_setup_theme', function () {
	load_theme_textdomain( 'theme', get_template_directory() . '/languages' );
	add_theme_support( 'title-tag' );

	register_nav_menus(
		array(
			'bottom1' => 'Footer1',
			'bottom2' => 'Footer2',
			'bottom3' => 'Footer3',
			'currency' => 'Currency',
			'language' => 'Language'
		)
	);
} );
 
if ( !function_exists( 'wpestate_chld_thm_cfg_parent_css' ) ):
    function wpestate_chld_thm_cfg_parent_css() {
        $parent_style = 'wpestate_style'; 
        wp_enqueue_style('bootstrap.min',get_theme_file_uri('/css/bootstrap.min.css'), array(), '1.0', 'all');  
        wp_enqueue_style('bootstrap-theme.min',get_theme_file_uri('/css/bootstrap-theme.min.css'), array(), '1.0', 'all');  
        
        $use_mimify     =   wpresidence_get_option('wp_estate_use_mimify','');
        $mimify_prefix  =   '';
        if($use_mimify==='yes'){
            $mimify_prefix  =   '.min';    
        }
        
        if($mimify_prefix===''){
            wp_enqueue_style($parent_style,get_template_directory_uri().'/style.css', array('bootstrap.min','bootstrap-theme.min'), '1.0', 'all');  
        }else{
            wp_enqueue_style($parent_style,get_template_directory_uri().'/style.min.css', array('bootstrap.min','bootstrap-theme.min'), '1.0', 'all');  
        }
        
        if ( is_rtl() ) {
           wp_enqueue_style( 'chld_thm_cfg_parent-rtl',  trailingslashit( get_template_directory_uri() ). '/rtl.css' );
	}
        wp_enqueue_style( 'wpestate-child-style',
            get_stylesheet_directory_uri() . '/style.css',
                array( $parent_style ),
                wp_get_theme()->get('Version')
        );
        
    }
endif;

load_child_theme_textdomain('wpresidence', get_stylesheet_directory().'/languages');
add_action( 'wp_enqueue_scripts', 'wpestate_chld_thm_cfg_parent_css' );

// Function child_deregister_styles
add_action('wp_enqueue_scripts', 'child_deregister_styles', 100);
function child_deregister_styles() {
	wp_dequeue_style('swiper');
	wp_deregister_style('swiper');
}

// Дерегістер скриптів fancybox
// Дерегістер скриптів fancybox
add_action('wp_enqueue_scripts', 'child_deregister_fancybox_scripts', 100);
function child_deregister_fancybox_scripts() {
	// Видалити скрипт jquery.fancybox.pack.js
	wp_dequeue_script('jquery.fancybox.pack');
	wp_deregister_script('jquery.fancybox.pack');

	// Видалити скрипт jquery.fancybox-thumbs.js
	wp_dequeue_script('jquery.fancybox-thumbs');
	wp_deregister_script('jquery.fancybox-thumbs');
}


// Add css
add_action( 'wp_enqueue_scripts', function () {

	wp_enqueue_style( 'swiper-bundle', get_stylesheet_directory_uri() . '/html/files/swiper/css/swiper-bundle.min.css', array(), filemtime( get_stylesheet_directory() . '/html/files/swiper/css/swiper-bundle.min.css' ) );
	wp_enqueue_style( 'tabs-style', get_stylesheet_directory_uri() . '/html/files/tabs/style.css', array(), filemtime( get_stylesheet_directory() . '/html/files/tabs/style.css' ) );
	wp_enqueue_style( 'select2', get_stylesheet_directory_uri() . '/html/files/select2/css/select2.min.css', array(), filemtime( get_stylesheet_directory() . '/html/files/select2/css/select2.min.css' ) );
	wp_enqueue_style( 'select2-custom', get_stylesheet_directory_uri() . '/html/files/select2/css/select2-custom.css', array(), filemtime( get_stylesheet_directory() . '/html/files/select2/css/select2-custom.css' ) );
	wp_enqueue_style( 'fancybox', get_stylesheet_directory_uri() . '/html/files/fancybox/css/fancybox.css', array(), filemtime( get_stylesheet_directory() . '/html/files/fancybox/css/fancybox.css' ) );

	wp_enqueue_style( 'template-css', get_stylesheet_directory_uri() . '/html/css/style.min.css', array(), filemtime( get_stylesheet_directory() . '/html/css/style.min.css' ) );
	wp_enqueue_style( 'theme-css', get_stylesheet_directory_uri() . '/style.css', array(), filemtime( get_stylesheet_directory() . '/style.css' ) );
	
	// Додаємо стилі для мульти-селектів з галочками
	wp_enqueue_style( 'select2-multi-checkboxes', get_stylesheet_directory_uri() . '/css/select2-multi-checkboxes.css', array('select2'), filemtime( get_stylesheet_directory() . '/css/select2-multi-checkboxes.css' ) );

	/*wp_enqueue_script( 'jquery' );
	wp_enqueue_script(
		'theme-main',
		get_stylesheet_directory_uri() . '/html/files/js/jquery-3.6.1.min.js',
		array(),
		filemtime( get_stylesheet_directory() . '/html/files/js/jquery-3.6.1.min.js' ),
		true
	);*/

} );

// Add js to footer
function add_custom_footer_scripts() {

	$script_version = filemtime( get_stylesheet_directory() . '/html/js/script.min.js' );
	wp_enqueue_script( 'script-min', get_stylesheet_directory_uri() . '/html/js/script.min.js', array(), $script_version, true );

	$script_version = filemtime( get_stylesheet_directory() . '/html/files/swiper/js/swiper-bundle.min.js' );
	wp_enqueue_script( 'swiper-bundle', get_stylesheet_directory_uri() . '/html/files/swiper/js/swiper-bundle.min.js', array(), $script_version, true );

	$script_version = filemtime( get_stylesheet_directory() . '/html/files/swiper/js/init.js' );
	wp_enqueue_script( 'init', get_stylesheet_directory_uri() . '/html/files/swiper/js/init.js', array(), $script_version, true );

	/*$script_version = filemtime( get_stylesheet_directory() . '/html/files/tabs/script.js' );
	wp_enqueue_script( 'script', get_stylesheet_directory_uri() . '/html/files/tabs/script.js', array(), $script_version, true );*/

	//
	$script_version = filemtime( get_stylesheet_directory() . '/html/files/select2/js/select2.min.js' );
	wp_enqueue_script( 'select2', get_stylesheet_directory_uri() . '/html/files/select2/js/select2.min.js', array(), $script_version, true );

	$script_version = filemtime( get_stylesheet_directory() . '/html/files/select2/js/init.js' );
	wp_enqueue_script( 'select2-init', get_stylesheet_directory_uri() . '/html/files/select2/js/init.js', array(), $script_version, true );

	$script_version = filemtime( get_stylesheet_directory() . '/html/files/fancybox/js/fancybox.umd.js' );
	wp_enqueue_script( 'fancybox', get_stylesheet_directory_uri() . '/html/files/fancybox/js/fancybox.umd.js', array(), $script_version, true );
	
	// Додаємо скрипт для мульти-селектів з галочками
	$script_version = filemtime( get_stylesheet_directory() . '/js/select2-multi-checkboxes.js' );
	wp_enqueue_script( 'select2-multi-checkboxes', get_stylesheet_directory_uri() . '/js/select2-multi-checkboxes.js', array('jquery', 'select2'), $script_version, true );
	
	// Додаємо скрипт ініціалізації мульти-селектів (після основного плагіну)
	$script_version = filemtime( get_stylesheet_directory() . '/js/multi-select-init.js' );
	wp_enqueue_script( 'multi-select-init', get_stylesheet_directory_uri() . '/js/multi-select-init.js', array('jquery', 'select2', 'select2-multi-checkboxes'), $script_version, true );

}
add_action('wp_footer', 'add_custom_footer_scripts');
//

// Shortcode [team]
add_shortcode('team', 'team');
function team() {
	ob_start();
	get_template_part('inc/team');
	return ob_get_clean();
}
//

// Shortcode [faq]
add_shortcode('faq', 'faq');
function faq() {
	ob_start();
	get_template_part('inc/faq');
	return ob_get_clean();
}
//

// Встановлюємо правильний template для результатів пошуку
add_filter('template_include', 'custom_search_template', 999);
function custom_search_template($template) {
    // Перевіряємо чи це сторінка /search-properties/ або є параметри пошуку
    if (strpos($_SERVER['REQUEST_URI'], '/search-properties/') !== false || !empty($_GET['id']) || !empty($_GET['property_type']) || !empty($_GET['city']) || !empty($_GET['district']) || !empty($_GET['rooms']) || !empty($_GET['price_from']) || !empty($_GET['price_to'])) {
        // Використовуємо наш власний template
        $search_template = get_stylesheet_directory() . '/search-results.php';
        if (file_exists($search_template)) {
            return $search_template;
        }
    }
    return $template;
}

// Створюємо глобальну змінну для результатів пошуку
add_action('wp', 'create_search_query');
function create_search_query() {
    global $wp_query_args_debug, $wp_query_country_debug, $wp_query_tax_debug;
    // Перевіряємо чи це сторінка /search-properties/ або є параметри пошуку
    if (strpos($_SERVER['REQUEST_URI'], '/search-properties/') !== false || !empty($_GET['id']) || !empty($_GET['property_type']) || !empty($_GET['city']) || !empty($_GET['district']) || !empty($_GET['rooms']) || !empty($_GET['price_from']) || !empty($_GET['price_to'])) {
        global $search_query;
        
        $args = [
            'post_type' => 'estate_property',
            'post_status' => 'publish',
            'posts_per_page' => 12,
            'paged' => get_query_var('paged') ? get_query_var('paged') : 1,
        ];
        
        $meta_query = [];
        $tax_query = [];
        if (!empty($_GET['id']) && $_GET['id'] !== '') {
            $args['post__in'] = [intval($_GET['id'])];
        }
        
        // Property type
        if (!empty($_GET['property_type'])) {
            $property_types = is_array($_GET['property_type']) ? $_GET['property_type'] : [$_GET['property_type']];
            // Декодуємо значення
            $property_types = array_map('rawurldecode', $property_types);
            $property_types = array_filter($property_types); // Видаляємо порожні значення
            if (!empty($property_types)) {
                $tax_query[] = [
                    'taxonomy' => 'property_category',
                    'field' => 'slug',
                    'terms' => array_map('sanitize_text_field', $property_types),
                    'operator' => 'IN',
                ];
            }
        }
        
        // City
        if (!empty($_GET['city'])) {
            $cities = is_array($_GET['city']) ? $_GET['city'] : [$_GET['city']];
            // Декодуємо значення
            $cities = array_map('rawurldecode', $cities);
            $cities = array_filter($cities); // Видаляємо порожні значення
            if (!empty($cities)) {
                $tax_query[] = [
                    'taxonomy' => 'property_county_state', // property_city
                    'field' => 'slug',
                    'terms' => array_map('sanitize_text_field', $cities),
                    'operator' => 'IN',
                ];
            }
        }
        
        // District
        if (!empty($_GET['district'])) {
            $districts = is_array($_GET['district']) ? $_GET['district'] : [$_GET['district']];
            // Декодуємо значення
            $districts = array_map('rawurldecode', $districts);
            $districts = array_filter($districts); // Видаляємо порожні значення
            if (!empty($districts)) {
                $tax_query[] = [
                    'taxonomy' => 'property_city', //property_county_state
                    'field' => 'slug',
                    'terms' => array_map('sanitize_text_field', $districts),
                    'operator' => 'IN',
                ];
            }
        }
        
        // Rooms (фільтр по таксономії room)
        if (!empty($_GET['rooms'])) {
            $rooms = is_array($_GET['rooms']) ? $_GET['rooms'] : [$_GET['rooms']];
            $rooms = array_filter($rooms); // Видаляємо порожні значення
            if (!empty($rooms)) {
                $tax_query[] = [
                    'taxonomy' => 'room',
                    'field'    => 'slug',
                    'terms'    => array_map('sanitize_text_field', $rooms),
                ];
            }
        }
        
        // Infrastructure
        if (!empty($_GET['infrastructure'])) {
            $infrastructure = is_array($_GET['infrastructure']) ? $_GET['infrastructure'] : [$_GET['infrastructure']];
            // Декодуємо значення
            $infrastructure = array_map('rawurldecode', $infrastructure);
            $infrastructure = array_filter($infrastructure); // Видаляємо порожні значення
            if (!empty($infrastructure)) {
                $tax_query[] = [
                    'taxonomy' => 'property_features',
                    'field' => 'slug',
                    'terms' => array_map('sanitize_text_field', $infrastructure),
                    'operator' => 'IN',
                ];
            }
        }
        
        // Advantages
        if (!empty($_GET['advantages'])) {
            $advantages = is_array($_GET['advantages']) ? $_GET['advantages'] : [$_GET['advantages']];
            // Декодуємо значення
            $advantages = array_map('rawurldecode', $advantages);
            $advantages = array_filter($advantages); // Видаляємо порожні значення
            if (!empty($advantages)) {
                $tax_query[] = [
                    'taxonomy' => 'property_advantages',
                    'field' => 'slug',
                    'terms' => array_map('sanitize_text_field', $advantages),
                    'operator' => 'IN',
                ];
            }
        }
        
        // Price
        $price_from = isset($_GET['price_from']) && $_GET['price_from'] !== '' ? floatval($_GET['price_from']) : '';
        $price_to = isset($_GET['price_to']) && $_GET['price_to'] !== '' ? floatval($_GET['price_to']) : '';
        if ($price_from !== '' && $price_to !== '') {
            $meta_query[] = [
                'key' => 'property_price',
                'value' => [$price_from, $price_to],
                'type' => 'NUMERIC',
                'compare' => 'BETWEEN',
            ];
        } elseif ($price_from !== '') {
            $meta_query[] = [
                'key' => 'property_price',
                'value' => $price_from,
                'type' => 'NUMERIC',
                'compare' => '>=',
            ];
        } elseif ($price_to !== '') {
            $meta_query[] = [
                'key' => 'property_price',
                'value' => $price_to,
                'type' => 'NUMERIC',
                'compare' => '<=',
            ];
        }
        
        // Площа (square_from, square_to)
        $square_from = isset($_GET['square_from']) && $_GET['square_from'] !== '' ? floatval($_GET['square_from']) : '';
        $square_to = isset($_GET['square_to']) && $_GET['square_to'] !== '' ? floatval($_GET['square_to']) : '';
        if ($square_from !== '' || $square_to !== '') {
            add_filter('posts_where', function($where) use ($square_from, $square_to) {
                global $wpdb;
                $min_expr = "CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(pm2.meta_value, ' ', 1), ' ', -1) AS UNSIGNED)";
                $max_expr = "CAST(SUBSTRING_INDEX(TRIM(SUBSTRING_INDEX(pm2.meta_value, '-', -1)), ' ', 1) AS UNSIGNED)";
                $conditions = [];
                if ($square_from !== '' && $square_to !== '') {
                    $conditions[] = "($min_expr <= " . intval($square_to) . " AND $max_expr >= " . intval($square_from) . ")";
                } elseif ($square_from !== '') {
                    $conditions[] = "($max_expr >= " . intval($square_from) . ")";
                } elseif ($square_to !== '') {
                    $conditions[] = "($min_expr <= " . intval($square_to) . ")";
                }
                if (!empty($conditions)) {
                    $where .= " AND EXISTS (\n                SELECT 1 FROM {$wpdb->postmeta} pm2\n                WHERE pm2.post_id = {$wpdb->posts}.ID\n                AND pm2.meta_key = 'd0bfd0bbd0bed189d0b0d0b4d18c'\n                AND " . implode(' AND ', $conditions) . "\n            )";
                }
                return $where;
            });
            /*add_filter('query', function($sql) {
                echo '<pre style=\"background:#ffe;border:1px solid #fc0;padding:10px;\">SQL:<br>' . esc_html($sql) . '</pre>';
                return $sql;
            });*/
        }

        // Этаж (floor_from, floor_to) - d18dd182d0b0d0b6
        $floor_from = isset($_GET['floor_from']) && $_GET['floor_from'] !== '' ? floatval($_GET['floor_from']) : '';
        $floor_to = isset($_GET['floor_to']) && $_GET['floor_to'] !== '' ? floatval($_GET['floor_to']) : '';
        if ($floor_from !== '' || $floor_to !== '') {
            add_filter('posts_where', function($where) use ($floor_from, $floor_to) {
                global $wpdb;
                $min_expr = "CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(pm3.meta_value, ' ', 1), ' ', -1) AS UNSIGNED)";
                $max_expr = "CAST(SUBSTRING_INDEX(TRIM(SUBSTRING_INDEX(pm3.meta_value, '-', -1)), ' ', 1) AS UNSIGNED)";
                $conditions = [];
                if ($floor_from !== '' && $floor_to !== '') {
                    $conditions[] = "($min_expr <= " . intval($floor_to) . " AND $max_expr >= " . intval($floor_from) . ")";
                } elseif ($floor_from !== '') {
                    $conditions[] = "($max_expr >= " . intval($floor_from) . ")";
                } elseif ($floor_to !== '') {
                    $conditions[] = "($min_expr <= " . intval($floor_to) . ")";
                }
                if (!empty($conditions)) {
                    $where .= " AND EXISTS (\n                SELECT 1 FROM {$wpdb->postmeta} pm3\n                WHERE pm3.post_id = {$wpdb->posts}.ID\n                AND pm3.meta_key = 'd18dd182d0b0d0b6'\n                AND " . implode(' AND ', $conditions) . "\n            )";
                }
                return $where;
            });
        }

        // Год строительства (year_from, year_to) - d0b3d0bed0b4-d181d182d180d0bed
        $year_from = isset($_GET['year_from']) && $_GET['year_from'] !== '' ? intval($_GET['year_from']) : '';
        $year_to = isset($_GET['year_to']) && $_GET['year_to'] !== '' ? intval($_GET['year_to']) : '';
        if ($year_from !== '' || $year_to !== '') {
            add_filter('posts_where', function($where) use ($year_from, $year_to) {
                global $wpdb;
                $year_expr = "CAST(pm4.meta_value AS UNSIGNED)";
                $conditions = [];
                if ($year_from !== '' && $year_to !== '') {
                    $conditions[] = "($year_expr >= " . intval($year_from) . " AND $year_expr <= " . intval($year_to) . ")";
                } elseif ($year_from !== '') {
                    $conditions[] = "($year_expr >= " . intval($year_from) . ")";
                } elseif ($year_to !== '') {
                    $conditions[] = "($year_expr <= " . intval($year_to) . ")";
                }
                if (!empty($conditions)) {
                    $where .= " AND EXISTS (\n                SELECT 1 FROM {$wpdb->postmeta} pm4\n                WHERE pm4.post_id = {$wpdb->posts}.ID\n                AND pm4.meta_key = 'd0b3d0bed0b4-d181d182d180d0bed'\n                AND " . implode(' AND ', $conditions) . "\n            )";
                }
                return $where;
            });
        }

        // До моря (sea_from, sea_to) - d0b4d0be-d0bcd0bed180d18f
        $sea_from = isset($_GET['sea_from']) && $_GET['sea_from'] !== '' ? floatval($_GET['sea_from']) : '';
        $sea_to = isset($_GET['sea_to']) && $_GET['sea_to'] !== '' ? floatval($_GET['sea_to']) : '';
        if ($sea_from !== '' || $sea_to !== '') {
            add_filter('posts_where', function($where) use ($sea_from, $sea_to) {
                global $wpdb;
                // Значення типу "50 м" або "100 м"
                $sea_expr = "CAST(SUBSTRING_INDEX(pm5.meta_value, ' ', 1) AS UNSIGNED)";
                $conditions = [];
                if ($sea_from !== '' && $sea_to !== '') {
                    $conditions[] = "($sea_expr >= " . intval($sea_from) . " AND $sea_expr <= " . intval($sea_to) . ")";
                } elseif ($sea_from !== '') {
                    $conditions[] = "($sea_expr >= " . intval($sea_from) . ")";
                } elseif ($sea_to !== '') {
                    $conditions[] = "($sea_expr <= " . intval($sea_to) . ")";
                }
                if (!empty($conditions)) {
                    $where .= " AND EXISTS (\n                SELECT 1 FROM {$wpdb->postmeta} pm5\n                WHERE pm5.post_id = {$wpdb->posts}.ID\n                AND pm5.meta_key = 'd0b4d0be-d0bcd0bed180d18f'\n                AND " . implode(' AND ', $conditions) . "\n            )";
                }
                return $where;
            });
        }
        
        // Action type (продажа/аренда)
        if (!empty($_GET['type'])) {
            $type_slug = $_GET['type'];
            // Мапимо value з форми на slug у базі (percent-encoded)
            $type_map = [
                'продажа' => '%d0%bf%d1%80%d0%be%d0%b4%d0%b0%d0%b6%d0%b0', // Продажа
                'аренда' => '%d0%b0%d1%80%d0%b5%d0%bd%d0%b4%d0%b0',        // Аренда
            ];
            if (isset($type_map[$type_slug])) {
                $action_slug = $type_map[$type_slug];
                $action_slug = rawurldecode($action_slug); // Декодуємо slug
                $tax_query[] = [
                    'taxonomy' => 'property_action_category',
                    'field' => 'slug',
                    'terms' => array_map('sanitize_text_field', [$action_slug]),
                    'operator' => 'IN',
                ];
            }
        }
        
        // Country
        if (!empty($_GET['country'])) {
            $country = sanitize_text_field($_GET['country']);
            $wp_query_country_debug = $country;
            $tax_query[] = [
                'taxonomy' => 'property_country',
                'field' => 'slug',
                'terms' => $country,
                'operator' => 'IN',
            ];
            $wp_query_tax_debug = $tax_query[count($tax_query)-1];
        }
        
        // Сортування
        if (isset($_GET['sort'])) {
            $sort = sanitize_text_field($_GET['sort']);
            $sort_direction = isset($_GET['sort_direction']) ? strtolower(sanitize_text_field($_GET['sort_direction'])) : 'ascending';
            $order = ($sort_direction === 'descending') ? 'DESC' : 'ASC';
            if ($sort === 'price') {
                $args['meta_key'] = 'property_price';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = $order;
            } elseif ($sort === 'rating') {
                $args['meta_key'] = 'd0b3d0bed0b4-d181d182d180d0bed';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = $order;
            } else {
                // default — залишаємо стандартне сортування
            }
        }

        if (!empty($meta_query)) {
            $args['meta_query'] = $meta_query;
        }
        if (!empty($tax_query)) {
            $args['tax_query'] = $tax_query;
        }
        $wp_query_args_debug = $args;
        $search_query = new WP_Query($args);
    }
}

// Shortcode [s1]
add_shortcode('searchform', 'searchform');
function searchform() {
	ob_start();
	get_template_part('inc/searchform');
	return ob_get_clean();
}
//

// Додаємо нову таксономію "Преимущества" для нерухомості
add_action('init', 'register_property_advantages_taxonomy');
function register_property_advantages_taxonomy() {
    $labels = array(
        'name'              => 'Преимущества',
        'singular_name'     => 'Преимущество',
        'search_items'      => 'Поиск преимуществ',
        'all_items'         => 'Все преимущества',
        'parent_item'       => 'Родительское преимущество',
        'parent_item_colon' => 'Родительское преимущество:',
        'edit_item'         => 'Редактировать преимущество',
        'update_item'       => 'Обновить преимущество',
        'add_new_item'      => 'Добавить новое преимущество',
        'new_item_name'     => 'Название нового преимущества',
        'menu_name'         => 'Преимущества',
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'property-advantages'),
        'show_in_rest'      => true, // Підтримка Gutenberg
    );

    register_taxonomy('property_advantages', array('estate_property'), $args);
}

// Додаємо нову таксономію "Страна" для нерухомості
add_action('init', 'register_property_country_taxonomy');
function register_property_country_taxonomy() {
    $labels = array(
        'name'              => 'Страны',
        'singular_name'     => 'Страна',
        'search_items'      => 'Поиск стран',
        'all_items'         => 'Все страны',
        'parent_item'       => 'Родительская страна',
        'parent_item_colon' => 'Родительская страна:',
        'edit_item'         => 'Редактировать страну',
        'update_item'       => 'Обновить страну',
        'add_new_item'      => 'Добавить новую страну',
        'new_item_name'     => 'Название новой страны',
        'menu_name'         => 'Страны',
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'property-country'),
        'show_in_rest'      => true, // Підтримка Gutenberg
    );

    register_taxonomy('property_country', array('estate_property'), $args);
}

// Add rooms column to estate property list (display only)
 
// Add rooms column to estate property list
add_filter( 'manage_estate_property_posts_columns', 'add_rooms_column' );
 
function add_rooms_column( $columns ) {
    $new_columns = array();
    foreach ( $columns as $key => $value ) {
        $new_columns[$key] = $value;
        if ( $key === 'title' ) {
            $new_columns['rooms'] = 'Кімнати';
        }
    }
    return $new_columns;
}
 
add_action( 'manage_estate_property_posts_custom_column', 'show_rooms_column_data', 10, 2 );
 
function show_rooms_column_data( $column, $post_id ) {
    if ( $column === 'rooms' ) {
        $rooms = get_post_meta( $post_id, 'rooms', true );
        if ( !empty( $rooms ) ) {
            echo esc_html( $rooms );
        } else {
            echo '—';
        }
    }
}

// Make rooms column sortable
add_filter( 'manage_edit-estate_property_sortable_columns', 'make_rooms_column_sortable' );
 
function make_rooms_column_sortable( $columns ) {
    $columns['rooms'] = 'rooms';
    return $columns;
}
 
// Handle sorting for rooms column
add_action( 'pre_get_posts', 'handle_rooms_column_sorting' );
 
function handle_rooms_column_sorting( $query ) {
    if ( !is_admin() ) {
        return;
    }
    
    $orderby = $query->get( 'orderby' );
    if ( $orderby === 'rooms' ) {
        $query->set( 'meta_key', 'rooms' );
        $query->set( 'orderby', 'meta_value' );
    }
}
