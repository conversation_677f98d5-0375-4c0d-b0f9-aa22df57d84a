<?php
if( ! is_singular('estate_property')){?>
 </div><!-- end content_wrapper started in header -->
<?php
}
?>
<?php if( ! is_home()){ ?>
</div> <!-- end class container -->
</div> <!-- end website wrapper ed-->
<?php } ?>

<footer class="footer">
    <div class="n_container">
        <div class="footer__items">
            <div class="footer__item">
                <div class="footer__wrapLogo">
                    <img src="<?php echo esc_url(get_stylesheet_directory_uri()) ?>/html/images/footer-logo.png" alt="">
                </div>
                <div class="footer__blockForm">
                    <p class="text-above-form">Подпишитесь на нашу рассылку, чтобы быть в курсе новых функций и релизов.</p>
	                <?php echo do_shortcode('[contact-form-7 id="c554d6d" title="Subscribe"]'); ?>
                    <p class="text-under-form">Подписываясь, вы соглашаетесь с нашей Политикой конфиденциальности и даете согласие на получение обновлений от нашей компании.</p>
                </div>
            </div>
            <div class="footer__item">
                <div class="footer__columns">
                    <div class="footer__column">
                        <h3>Column One</h3>
	                    <?php $args = array(
		                    'theme_location' => 'bottom1',
		                    'container'=> false,
		                    'menu_class' => 'footer__list',
		                    'menu_id' => 'bottom-nav1',
		                    'fallback_cb' => false
	                    );
	                    wp_nav_menu($args); ?>
                    </div>
                    <div class="footer__column">
                        <div class="footer__columnContent">
                            <h3>Follow Us</h3>
	                        <?php $args = array(
		                        'theme_location' => 'bottom2',
		                        'container'=> false,
		                        'menu_class' => 'footer__listContact',
		                        'menu_id' => 'bottom-nav2',
		                        'fallback_cb' => false
	                        );
	                        wp_nav_menu($args); ?>
                            <a href="mailto:<EMAIL>" class="footer__email"><EMAIL></a>
                            <address>Район Махмутлар, ул. Ватан, комплекс Торос 2, No: 5\Е 07450, Алания/ Анталья</address>
                            <ul class="footer__social">
                                <li class="fb-icon"><a href="https://www.facebook.com/UPTRENDHOMES1?mibextid=LQQJ4d" target="_blank"></a></li>
                                <li class="yt-icon"><a href="https://www.youtube.com/@uptrendhomes" target="_blank"></a></li>
                                <li class="is-icon"><a href="https://www.instagram.com/uptrendhomes/?igsh=MTA0ZW1mb3l5anljMA%3D%3D" target="_blank"></a></li>
                                <!--<li class="ws-icon"><a href="#"></a></li>-->
                                <!--<li class="tg-icon"><a href="#"></a></li>-->
                                <!--<li class="vb-icon"><a href="#"></a></li>-->
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer__bottom">
            <p class="footer__copyright">&copy; <?php echo date('Y'); ?> Uptrend. All rights reserved.</p>
            <?php $args = array(
                'theme_location' => 'bottom3',
                'container'=> false,
                'menu_class' => 'footer__menu',
                'menu_id' => 'bottom-nav3',
                'fallback_cb' => false
            );
            wp_nav_menu($args); ?>
        </div>
    </div>
    <img class="footer__element left" src="<?php echo esc_url(get_stylesheet_directory_uri()) ?>/html/images/svg/footer-element-left.svg" alt="element-left">
    <img class="footer__element right" src="<?php echo esc_url(get_stylesheet_directory_uri()) ?>/html/images/svg/footer-element-right.svg" alt="element-right">
</footer>

<?php 
wp_footer();?>

<script>
    initializeSlider('objects-slider-1', 'objects-pagination-slider-1');
    initializeSlider('objects-slider-2', 'objects-pagination-slider-2');
    initializeSlider('objects-slider-3', 'objects-pagination-slider-3');
    initializeSlider('objects-slider-4', 'objects-pagination-slider-4');
    initializeSlider('objects-slider-5', 'objects-pagination-slider-5');
    initializeSlider('objects-slider-6', 'objects-pagination-slider-6');
    initializeSlider('objects-slider-7', 'objects-pagination-slider-7');
    initializeSlider('objects-slider-8', 'objects-pagination-slider-8');
</script>

<script>

    // Most Popular Objects
    Fancybox.bind(".js-object-gallery-1", {groupAll: true,on: {ready: (fancybox) => {}}});
    Fancybox.bind(".js-object-gallery-2", {groupAll: true,on: {ready: (fancybox) => {}}});
    Fancybox.bind(".js-object-gallery-3", {groupAll: true,on: {ready: (fancybox) => {}}});

    // Подсчет изображений в каждом <p class="quantity-with-icon"> и вывод числа возле иконки
    document.querySelectorAll('.newest-objects__object.swiper-slide').forEach(objectBlock => {
        const quantityBlock = objectBlock.querySelector('.quantity-with-icon');
        if (quantityBlock) {
            const numberSpan = quantityBlock.querySelector('.number-gallery');
            const links = quantityBlock.querySelectorAll('a[href]');

            if (numberSpan) {
                numberSpan.textContent = links.length;
            }
        }
    });

</script>

<script><!--Sort script-->
    document.addEventListener('DOMContentLoaded', function() {

        // сортировка направления
        let sortByDirection = document.querySelector('.sort-by-direction');
        let sortDirectionInput = document.getElementById('sort-direction-input');
        if (sortByDirection) {
            sortByDirection.addEventListener('click', function () {
                if (!this.classList.contains('ascending') && !this.classList.contains('descending')) {
                    // Первый клик — добавить ascending
                    this.classList.add('ascending');
                    if (sortDirectionInput) sortDirectionInput.value = 'ascending';
                } else if (this.classList.contains('ascending')) {
                    // Был ascending — меняем на in-descending-order
                    this.classList.remove('ascending');
                    this.classList.add('descending');
                    if (sortDirectionInput) sortDirectionInput.value = 'descending';
                } else {
                    // Был in-descending-order — меняем на ascending
                    this.classList.remove('descending');
                    this.classList.add('ascending');
                    if (sortDirectionInput) sortDirectionInput.value = 'ascending';
                }
            });
        }

        // вид карточек
        let typeOfTiles = document.querySelector('.type-of-tiles');
        let apartmentsBlock = document.querySelector('.apartments');
        if (typeOfTiles && apartmentsBlock) {
            typeOfTiles.addEventListener('change', (event) => {
                if (event.target.name === 'type-of-tiles') {
                    console.log(event.target.value);
                    const selectedValue = event.target.value;
                    apartmentsBlock.classList.remove('grid', 'list'); // Удалим оба класса на всякий случай
                    apartmentsBlock.classList.add(selectedValue); // Добавим нужный
                }
            });
        }
    });
</script>

<script><!--Tabs script-->
    document.addEventListener("DOMContentLoaded", function () {
        var tabsBlocks = document.querySelectorAll('.tabs');

        tabsBlocks.forEach(function (tabsBlock) {
            var tabs = tabsBlock.querySelectorAll('.tabs__wrap-tab .tab');
            var contents = tabsBlock.querySelectorAll('.tabContent');

            tabs.forEach(function (tab) {
                tab.addEventListener('click', function () {
                    var tabId = this.getAttribute('data-tab');
                    var targetContent = tabsBlock.querySelector('.tabContent[data-tabcontent="' + tabId + '"]');

                    tabs.forEach(function (t) {
                        t.classList.remove('tab-active');
                    });

                    contents.forEach(function (c) {
                        c.classList.remove('content-active');
                    });

                    this.classList.add('tab-active');
                    if (targetContent) {
                        targetContent.classList.add('content-active');
                    }
                });
            });
        });
    });
</script>

</body>
</html>
