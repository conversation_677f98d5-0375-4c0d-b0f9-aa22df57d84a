/**
 * JavaScript для нової форми пошуку нерухомості
 * Підтримує новий формат параметрів
 */

(function($) {
    'use strict';

    // Глобальні змінні
    var searchTimeout;
    var currentPage = 1;

    /**
     * Головна функція для AJAX пошуку
     */
    function wpestateNewSearchAjax(newpage, customParams) {
        
        // Показуємо лоадер
        $('#listing_loader').show();
        $('.listing_loader_title').hide();
        
        // Очищаємо контейнер
        $('#listing_ajax_container').empty();
        
        // Підготовка даних для відправки
        var searchData = {
            'action': 'wpestate_ajax_new_search',
            'newpage': newpage || 1,
            'security': $('#wpestate_ajax_filtering').val() || wpestate_new_search.nonce
        };
        
        // Якщо передані кастомні параметри, використовуємо їх
        if (customParams) {
            $.extend(searchData, customParams);
        } else {
            // Інакше збираємо дані з форми
            searchData = collectFormData(searchData);
        }
        
        // Відправляємо AJAX запит
        $.ajax({
            type: 'POST',
            url: wpestate_new_search.ajax_url,
            dataType: 'json',
            data: searchData,
            success: function(response) {
                handleSearchResponse(response);
            },
            error: function(xhr, status, error) {
                handleSearchError(error);
            },
            complete: function() {
                $('#listing_loader').hide();
            }
        });
    }

    /**
     * Збір даних з форми пошуку
     */
    function collectFormData(baseData) {
        
        // Тип операції (sale/rent)
        var selectedType = $('input[name="type"]:checked').val();
        if (selectedType) {
            baseData.type = selectedType;
        }
        
        // Тип нерухомості
        var propertyType = $('#search-property-type').val();
        if (propertyType) {
            baseData.property_type = propertyType;
        }
        
        // Місто
        var city = $('#search-city').val();
        if (city) {
            baseData.city = city;
        }
        
        // Район
        var district = $('#search-district').val();
        if (district) {
            baseData.district = district;
        }
        
        // Кімнати
        var rooms = $('#search-rooms').val();
        if (rooms) {
            baseData.rooms = rooms;
        }
        
        // Ціна
        var priceFrom = $('input[name="price_from"]').val();
        var priceTo = $('input[name="price_to"]').val();
        if (priceFrom) {
            baseData.price_from = priceFrom;
        }
        if (priceTo) {
            baseData.price_to = priceTo;
        }
        
        // ID нерухомості
        var propertyId = $('input[name="property_id"]').val();
        if (propertyId) {
            baseData.property_id = propertyId;
        }
        
        // Площа
        var squareFrom = $('input[name="square_from"]').val();
        var squareTo = $('input[name="square_to"]').val();
        if (squareFrom) {
            baseData.square_from = squareFrom;
        }
        if (squareTo) {
            baseData.square_to = squareTo;
        }
        
        // Поверх
        var floorFrom = $('input[name="floor_from"]').val();
        var floorTo = $('input[name="floor_to"]').val();
        if (floorFrom) {
            baseData.floor_from = floorFrom;
        }
        if (floorTo) {
            baseData.floor_to = floorTo;
        }
        
        // Рік будівництва
        var yearFrom = $('input[name="year_from"]').val();
        var yearTo = $('input[name="year_to"]').val();
        if (yearFrom) {
            baseData.year_from = yearFrom;
        }
        if (yearTo) {
            baseData.year_to = yearTo;
        }
        
        // Відстань до моря
        var seaFrom = $('input[name="sea_from"]').val();
        var seaTo = $('input[name="sea_to"]').val();
        if (seaFrom) {
            baseData.sea_from = seaFrom;
        }
        if (seaTo) {
            baseData.sea_to = seaTo;
        }
        
        // Інфраструктура
        var infrastructure = $('#search-infrastructure').val();
        if (infrastructure) {
            baseData.infrastructure = infrastructure;
        }
        
        // Переваги
        var advantages = $('#search-advantages').val();
        if (advantages) {
            baseData.advantages = advantages;
        }
        
        // Ключове слово
        var keyword = $('input[name="keyword"]').val();
        if (keyword) {
            baseData.keyword = keyword;
        }
        
        // Геолокація
        var geoLat = $('#geolocation_lat').val();
        var geoLong = $('#geolocation_long').val();
        var geoRadius = $('#geolocation_radius').val();
        
        if (geoLat && geoLong) {
            baseData.geo_lat = geoLat;
            baseData.geo_long = geoLong;
            if (geoRadius) {
                baseData.geo_rad = geoRadius;
            }
        }
        
        return baseData;
    }

    /**
     * Обробка відповіді від сервера
     */
    function handleSearchResponse(response) {
        
        if (response.success) {
            // Оновлюємо контейнер з результатами
            $('#listing_ajax_container').html(response.cards);
            
            // Оновлюємо заголовок з кількістю результатів
            if (response.found > 0) {
                $('.entry-title.title_prop').removeClass('half_results').text(
                    'Found ' + response.found + ' properties'
                );
            }
            
            // Перезапускаємо JavaScript після AJAX
            if (typeof wpestate_restart_js_after_ajax === 'function') {
                wpestate_restart_js_after_ajax();
            }
            
        } else {
            // Показуємо повідомлення про відсутність результатів
            $('#listing_ajax_container').html(response.cards);
            $('.entry-title.title_prop').addClass('half_results').text('No properties found');
        }
        
        // Оновлюємо URL без перезавантаження сторінки
        updateURL();
    }

    /**
     * Обробка помилок
     */
    function handleSearchError(error) {
        console.error('Search error:', error);
        $('#listing_ajax_container').html(
            '<div class="search_error">An error occurred while searching. Please try again.</div>'
        );
    }

    /**
     * Оновлення URL без перезавантаження сторінки
     */
    function updateURL() {
        var searchData = collectFormData({});
        var queryString = $.param(searchData);
        
        if (queryString) {
            var newURL = window.location.pathname + '?' + queryString;
            window.history.pushState({}, '', newURL);
        }
    }

    /**
     * Функція для пошуку з затримкою (debounce)
     */
    function debouncedSearch() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            wpestateNewSearchAjax(1);
        }, 1000);
    }

    /**
     * Ініціалізація обробників подій
     */
    function initEventHandlers() {
        
        // Обробник для кнопки пошуку
        $('.search-form__submit').on('click', function(e) {
            e.preventDefault();
            wpestateNewSearchAjax(1);
        });
        
        // Автоматичний пошук при зміні селектів
        $('.search-form select').on('change', function() {
            wpestateNewSearchAjax(1);
        });
        
        // Автоматичний пошук при зміні радіо кнопок
        $('.search-form input[type="radio"]').on('change', function() {
            wpestateNewSearchAjax(1);
        });
        
        // Пошук з затримкою для числових полів
        $('.search-form input[type="number"]').on('input', function() {
            debouncedSearch();
        });
        
        // Пошук з затримкою для текстових полів
        $('.search-form input[type="text"]').on('input', function() {
            debouncedSearch();
        });
        
        // Обробник для пагінації
        $(document).on('click', '.pagination_wrapper a', function(e) {
            e.preventDefault();
            var href = $(this).attr('href');
            var page = getPageFromURL(href);
            if (page) {
                wpestateNewSearchAjax(page);
            }
        });
        
        // Обробник для сортування
        $('.order_by_property').on('change', function() {
            var order = $(this).val();
            var searchData = collectFormData({});
            searchData.order = order;
            wpestateNewSearchAjax(1, searchData);
        });
    }

    /**
     * Отримання номера сторінки з URL
     */
    function getPageFromURL(url) {
        var match = url.match(/paged=(\d+)/);
        return match ? parseInt(match[1]) : 1;
    }

    /**
     * Функція для програмного запуску пошуку
     */
    window.wpestateNewSearch = function(params, page) {
        wpestateNewSearchAjax(page || 1, params);
    };

    /**
     * Функція для створення URL пошуку
     */
    window.wpestateCreateSearchURL = function(params) {
        var queryString = $.param(params);
        return window.location.pathname + '?' + queryString;
    };

    /**
     * Функція для отримання поточних параметрів пошуку
     */
    window.wpestateGetCurrentSearchParams = function() {
        return collectFormData({});
    };

    /**
     * Ініціалізація при завантаженні документа
     */
    $(document).ready(function() {
        initEventHandlers();
        
        // Якщо є параметри в URL, виконуємо пошук
        if (window.location.search) {
            var urlParams = new URLSearchParams(window.location.search);
            var hasSearchParams = false;
            
            // Перевіряємо чи є параметри пошуку
            var searchParamNames = [
                'property_type', 'type', 'city', 'district', 'rooms',
                'price_from', 'price_to', 'property_id', 'square_from', 'square_to',
                'floor_from', 'floor_to', 'year_from', 'year_to', 'sea_from', 'sea_to',
                'infrastructure', 'advantages', 'keyword'
            ];
            
            searchParamNames.forEach(function(param) {
                if (urlParams.has(param)) {
                    hasSearchParams = true;
                }
            });
            
            if (hasSearchParams) {
                // Заповнюємо форму параметрами з URL
                fillFormFromURL(urlParams);
                
                // Виконуємо пошук
                wpestateNewSearchAjax(1);
            }
        }
    });

    /**
     * Заповнення форми параметрами з URL
     */
    function fillFormFromURL(urlParams) {
        
        // Тип операції
        if (urlParams.has('type')) {
            $('input[name="type"][value="' + urlParams.get('type') + '"]').prop('checked', true);
        }
        
        // Тип нерухомості
        if (urlParams.has('property_type')) {
            $('#search-property-type').val(urlParams.get('property_type'));
        }
        
        // Місто
        if (urlParams.has('city')) {
            $('#search-city').val(urlParams.get('city'));
        }
        
        // Район
        if (urlParams.has('district')) {
            $('#search-district').val(urlParams.get('district'));
        }
        
        // Кімнати
        if (urlParams.has('rooms')) {
            $('#search-rooms').val(urlParams.get('rooms')).prop('checked', true);
        }
        
        // Ціна
        if (urlParams.has('price_from')) {
            $('input[name="price_from"]').val(urlParams.get('price_from'));
        }
        if (urlParams.has('price_to')) {
            $('input[name="price_to"]').val(urlParams.get('price_to'));
        }
        
        // Інші поля...
        var textFields = [
            'property_id', 'square_from', 'square_to', 'floor_from', 'floor_to',
            'year_from', 'year_to', 'sea_from', 'sea_to', 'keyword'
        ];
        
        textFields.forEach(function(field) {
            if (urlParams.has(field)) {
                $('input[name="' + field + '"]').val(urlParams.get(field));
            }
        });
        
        // Селекти
        var selectFields = ['infrastructure', 'advantages'];
        selectFields.forEach(function(field) {
            if (urlParams.has(field)) {
                $('#search-' + field).val(urlParams.get(field));
            }
        });
    }

})(jQuery); 