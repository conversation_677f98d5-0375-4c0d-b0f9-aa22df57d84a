<?php
// Wp Estate Pack

// Функція для призначення термінів таксономії room на основі мета поля rooms
/*function assign_room_taxonomies_from_meta() {
    // Отримуємо всі записи типу estate_property
    $args = array(
        'post_type' => 'estate_property',
        'post_status' => 'publish',
        'posts_per_page' => -1, // Всі записи
        'meta_query' => array(
            array(
                'key' => 'rooms',
                'compare' => 'EXISTS',
            ),
        ),
    );

    $properties = new WP_Query($args);
    
    if ($properties->have_posts()) {
        while ($properties->have_posts()) {
            $properties->the_post();
            $post_id = get_the_ID();
            
            // Отримуємо значення мета поля rooms
            $rooms_value = get_post_meta($post_id, 'rooms', true);
            
            if (!empty($rooms_value)) {
                // Розбиваємо значення на окремі терміни (розділювач - пробіл)
                $room_terms = explode(' ', trim($rooms_value));
                
                // Масив для зберігання ID термінів для цього поста
                $term_ids = array();
                
                foreach ($room_terms as $room_term) {
                    $room_term = trim($room_term);
                    
                    if (!empty($room_term)) {
                        // Перевіряємо чи існує термін в таксономії room
                        $existing_term = get_term_by('name', $room_term, 'room');
                        
                        if (!$existing_term) {
                            // Створюємо новий термін
                            $term_result = wp_insert_term($room_term, 'room');
                            
                            if (!is_wp_error($term_result)) {
                                $term_ids[] = $term_result['term_id'];
                            }
                        } else {
                            // Якщо термін вже існує, додаємо його ID
                            $term_ids[] = $existing_term->term_id;
                        }
                    }
                }
                
                // Призначаємо всі терміни до поста
                if (!empty($term_ids)) {
                    wp_set_object_terms($post_id, $term_ids, 'room', true);
                }
            }
        }
        wp_reset_postdata();
    }
}

// Викликаємо функцію при завантаженні 404 сторінки
assign_room_taxonomies_from_meta();*/

get_header();
$wpestate_options=wpestate_page_details('');

?>
<?php if ( ! function_exists( 'elementor_theme_do_location' ) || ! elementor_theme_do_location( 'single' ) ) { ?>
<div class="row">
    <?php get_template_part('templates/breadcrumbs'); ?>
    <div class="<?php print esc_html($wpestate_options['content_class']);?> ">

         <?php get_template_part('templates/ajax_container'); ?>

           <h1 class="entry-title"><?php _e('Page not found','wpresidence');?></h1>

            <div class="single-content content404 col-md-12">
                <p>
                <?php _e( 'We\'re sorry. Your page could not be found, But you can check our latest listings & articles', 'wpresidence' ); ?>
                </p>

                <div class="list404">
                <h3><?php _e('Latest Listings','wpresidence');?></h3>
                <?php

                $args = array(
                     'post_type'        => 'estate_property',
                     'post_status'      => 'publish',
                     'paged'            => 0,
                     'posts_per_page'   => 10,
                 );

                 $recent_posts = new WP_Query($args);
                   print '<ul>';
                   while ($recent_posts->have_posts()): $recent_posts->the_post();
                        print '<li><a href="'. esc_url( get_permalink() ).'">'.get_the_title().'</a></li>';
                   endwhile;
                   print '</ul>';
                ?>
                </div>

                <div class="list404">
                <h3><?php _e('Latest Articles','wpresidence');?></h3>
                <?php
                  $args = array(
                     'post_type'        => 'post',
                     'post_status'      => 'publish',
                     'paged'            => 0,
                     'posts_per_page'   => 10,
                 );

                 $recent_posts = new WP_Query($args);
                   print '<ul>';
                   while ($recent_posts->have_posts()): $recent_posts->the_post();
                        print '<li><a href="'. esc_url( get_permalink() ).'">'.get_the_title().'</a></li>';
                   endwhile;
                   print '</ul>';
                ?>
                </div>

            </div><!-- single content-->

    </div>
    </div>
<?php } ?>

<?php
include get_theme_file_path('sidebar.php');
?>

<?php get_footer(); ?>
