<?php
$current_sort = isset($_GET['sort']) ? $_GET['sort'] : '';
$sort_direction = isset($_GET['sort_direction']) ? $_GET['sort_direction'] : 'ascending';
?>
<div class="right">
    <div class="content">
        <form method="get" id="sort-form" class="content" action="<?php echo esc_url( remove_query_arg( array('sort', 'sort_direction'), $_SERVER['REQUEST_URI'] ) ); ?>">
            <?php
            // Зберігаємо всі поточні GET-параметри, крім sort та sort_direction
            foreach ($_GET as $key => $value) {
                if ($key === 'sort' || $key === 'sort_direction') continue;
                if (is_array($value)) {
                    foreach ($value as $v) {
                        echo '<input type="hidden" name="' . esc_attr($key) . '[]" value="' . esc_attr($v) . '">';
                    }
                } else {
                    echo '<input type="hidden" name="' . esc_attr($key) . '" value="' . esc_attr($value) . '">';
                }
            }
            ?>
            <input type="hidden" name="sort_direction" value="<?php echo $sort_direction === 'ascending' ? 'descending' : 'ascending'; ?>">
            <input type="hidden" name="sort" value="<?php echo esc_attr($current_sort); ?>">
            
            <button type="submit" class="sort-by-direction <?php echo $sort_direction === 'descending' ? 'descending' : 'ascending'; ?>" id="sort-direction-button">
                <svg width="26" height="27" viewBox="0 0 26 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g class="arrow-down">
                        <path d="M21 16.0714L17.4444 19.5L13.8889 16.0714" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M17.4444 19.5V7.5" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                    </g>
                    <g class="arrow-up">
                        <path d="M5 10.9286L8.55556 7.5L12.1111 10.9286" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M8.55556 7.5V19.5" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                    </g>
                </svg>
            </button>
            
            <div class="select select-currency">
				<div class="select__header select-currency__header">
                    <p><?= __('Сортировать', 'wpresidence-child'); ?>:</p>
                    <span class="select__current select-currency__current">
                        <?php
                        if ($current_sort === 'price') {
                            echo __('По цене', 'wpresidence-child');
                        } elseif ($current_sort === 'rating') {
                            echo __('Год строительства', 'wpresidence-child');
                        } else {
                            echo __('По умолчанию', 'wpresidence-child');
                        }
                        ?>
                    </span>
                </div>
                <div class="select__body">
                    <button type="submit" name="sort" value="default" class="select__item select-currency__item" <?php echo empty($current_sort) ? 'style="display:none;"' : ''; ?>><?php _e('По умолчанию', 'wpresidence-child'); ?></button>
                    <button type="submit" name="sort" value="price" class="select__item select-currency__item"><?php _e('По цене', 'wpresidence-child'); ?></button>
                    <button type="submit" name="sort" value="rating" class="select__item select-currency__item"><?php _e('Год строительства', 'wpresidence-child'); ?></button>
                </div>
			</form>
		</div>
		<div class="type-of-tiles">
			<input type="radio" name="type-of-tiles" value="list">
			<input type="radio" name="type-of-tiles" value="grid" checked>
		</div>
	</div>
</div>