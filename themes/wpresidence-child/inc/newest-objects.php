<?php
// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
// The following functions are provided by WordPress core and are available in theme templates:
// get_the_ID, wp_reset_postdata, esc_html_e, esc_url, get_post_type_archive_link
?>
<section class="newest-objects">
	<div class="n_container">
		<h2 class="newest-objects__title"><?= __('Новые обьекты', 'wpresidence-child'); ?></h2>
		<p class="newest-objects__desc">
			<?= __('Мы знаем всё о недвижимости в Аланье и Северном Кипре, потому что сами живём здесь, растим детей и строим будущее.', 'wpresidence-child'); ?>
            </p>
		<div id="newest-objects-slider" class="newest-objects__slider swiper-conteiner">
			<div class="swiper-wrapper">
				<?php
				$args = array(
					'post_type'      => 'estate_property',
					'posts_per_page' => 4,
					'orderby'        => 'date',
					'order'          => 'DESC',
				);
				$properties_query = new WP_Query( $args );
				if ( $properties_query->have_posts() ) :
					while ( $properties_query->have_posts() ) : $properties_query->the_post();
						$city = strip_tags(get_the_term_list(get_the_ID(), 'property_city', '', ', ', ''));
						$square = get_post_meta(get_the_ID(), 'd0bfd0bbd0bed189d0b0d0b4d18c', true); // Площадь
						$terms = get_the_terms(get_the_ID(), 'room'); // Терміни таксономії room
						if ($terms && !is_wp_error($terms)) {
						    $room_names = wp_list_pluck($terms, 'name');
						    $rooms = implode(', ', $room_names);
						} else {
						    $rooms = '';
						}
						$to_sea = get_post_meta(get_the_ID(), 'd0b4d0be-d0bcd0bed180d18f', true); // До моря
						$price = get_post_meta(get_the_ID(), 'd186d0b5d0bdd0b0', true); // Текстова ціна
						$currency = get_post_meta(get_the_ID(), 'property_currency', true);
						$object_id = get_the_ID();
					?>
					<div class="newest-objects__object swiper-slide">
						<div class="object__blockImage">
							<div class="tags">
								<?php
								$tags = get_the_terms( get_the_ID(), 'property_action_category' );
								if ( $tags && ! is_wp_error( $tags ) ) {
									foreach ( $tags as $tag ) {
										$class = 'tag';
										if ( $tag->name === 'Продажа' ) {
											$class .= ' vivid-orange';
										} elseif ( $tag->name === 'Аренда' ) {
											$class .= ' vivid-red';
										}
										echo '<span class="' . esc_attr($class) . '">' . esc_html( $tag->name ) . '</span>';
									}
								}
								?>
							</div>
							<a href="<?php the_permalink(); ?>">
								<?php if ( has_post_thumbnail() ) : ?>
									<img loading="lazy" src="<?php the_post_thumbnail_url( 'large' ); ?>" alt="<?php the_title_attribute(); ?>">
								<?php else : ?>
									<img loading="lazy" src="<?php echo esc_url( get_stylesheet_directory_uri() ); ?>/html/images/photo-object-default.png" alt="Default Image">
								<?php endif; ?>
							</a>
							<p class="object__id">ID: <?php echo esc_html( $object_id ); ?></p>
						</div>
						<h3 class="object__name"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
						<ul class="object__list">
							<li class="location-icon"><?= __('Расположение', 'wpresidence-child'); ?>: <strong><?php echo esc_html( $city ? $city : '-' ); ?></strong></li>
							<li class="square-icon"><?= __('Площадь', 'wpresidence-child'); ?>: <strong><?php echo esc_html( $square ? $square : '-' ); ?></strong></li>
							<li class="rooms-icon"><?= __('Комнат', 'wpresidence-child'); ?>: <strong><?php echo esc_html( $rooms ? $rooms : '-' ); ?></strong></li>
							<li class="to-sea-icon"><?= __('До моря', 'wpresidence-child'); ?>: <strong><?php echo esc_html( $to_sea ? $to_sea : '-' ); ?></strong></li>
						</ul>
						<a href="<?php the_permalink(); ?>" class="object__button"><span class="currency"><?php echo esc_html( $currency ? $currency : '€' ); ?></span> <?php echo esc_html( $price ? $price : '-' ); ?></a>
					</div>
					<?php
					endwhile;
					wp_reset_postdata();
				else :
					?>
					<p><?php esc_html_e( 'No properties found.', 'wpresidence-child' ); ?></p>
					<?php
				endif;
				?>
			</div>
			<div id="newest-objects-pagination" class="swiper-pagination square-dots"></div>
		</div>
		<a href="<?php echo esc_url( get_post_type_archive_link( 'estate_property' ) ); ?>" class="newest-objects__button"><?= __('ВСЕ ОБЬЕКТЫ', 'wpresidence-child'); ?></a>
	</div>
</section>