<?php
// Перевіряємо чи завантажений WordPress
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

if (!function_exists('get_terms') || !function_exists('is_wp_error') || !function_exists('esc_attr') || !function_exists('esc_html')) {
    return;
}

// Ensure we're in the WordPress environment
if (!function_exists('wp_kses')) {
    return;
}
?>
<div class="block-search-form">
	<div class="n_container">
		<div class="tabs">
			<!--
				* клас block-switching забороняє взаємодіяти з перемиканням вкладок.
				* Просто прибери клас якщо вкладки знадобляться
			-->
			<div class="tabs__wrap-tab"><!-- block-switching -->
				<div data-tab="турция" class="tab tab-active">Турция</div>
				<div data-tab="кипр" class="tab">Кипр</div>
			</div>
			<div class="tabContent content-active">
			<form class="search-form" method="get" action="/search-properties/">
<input type="hidden" name="country" id="search-country" value="турция">
<div class="search-form__content">
<div class="search-form__column type">
<label><?= esc_html__('Тип', 'wpresidence-child'); ?></label>
<?php
$type_active = isset($_GET['type']) ? $_GET['type'] : '';
?>
<div class="wrap-field wrap-radio">
	<label>
		<input type="radio" name="type" class="sale" value="продажа" <?php if ($type_active === 'аренда') echo ''; else echo 'checked'; ?>>
		<span><?= esc_html__('Продажа', 'wpresidence-child'); ?></span>
	</label>
	<label>
		<input type="radio" name="type" class="rent" value="аренда" <?php if ($type_active === 'аренда') echo 'checked'; ?>>
		<span><?= esc_html__('Аренда', 'wpresidence-child'); ?></span>
	</label>
</div>
</div>
<div class="search-form__column select">
<label><?= esc_html__('Тип недвижимости', 'wpresidence-child'); ?></label>
<div class="wrap-field js-wrap-select-form wrap-select">
	<select id="search-property-type" name="property_type[]" class="select2-multi-checkboxes" multiple="multiple" data-placeholder="Все">
		<?php
		$terms = get_terms([
			'taxonomy' => 'property_category',
			'post_type' => 'estate_property',
			'hide_empty' => false
		]);
		if (!is_wp_error($terms) && !empty($terms) && is_array($terms)) {
			foreach ($terms as $term) {
				echo '<option value="' . esc_attr($term->slug) . '">' . esc_html($term->name) . '</option>';
			}
		}
		?>
	</select>
</div>
</div>
<div class="search-form__column select">
<label><?= esc_html__('Город', 'wpresidence-child'); ?></label>
<div class="wrap-field js-wrap-select-form wrap-select">
	<select id="search-city" name="city[]" class="select2-multi-checkboxes" multiple="multiple" data-placeholder="Все">
		<?php
		$cities = get_terms([
			'taxonomy' => 'property_county_state', // property_city
			'post_type' => 'estate_property',
			'hide_empty' => false
		]);
		if (!is_wp_error($cities) && !empty($cities) && is_array($cities)) {
			foreach ($cities as $city) {
				// Додаємо data-city-id для встановлення зв'язку з районами
				echo '<option value="' . esc_attr($city->slug) . '" data-city-id="' . esc_attr($city->term_id) . '">' . esc_html($city->name) . '</option>';
			}
		}
		?>
	</select>
</div>
</div>
<div class="search-form__column select">
<label><?= esc_html__('Район', 'wpresidence-child'); ?></label>
<div class="wrap-field js-wrap-select-form wrap-select">
	<select id="search-district" name="district[]" class="select2-multi-checkboxes" multiple="multiple" data-placeholder="Все">
		<?php
		// Отримуємо всі міста з таксономії property_county_state для створення мапи ID
		$cities = get_terms([
			'taxonomy' => 'property_county_state',
			'hide_empty' => false
		]);

		$city_name_to_id_map = [];
		if (!is_wp_error($cities) && !empty($cities)) {
			foreach ($cities as $city) {
				$city_name_to_id_map[mb_strtolower($city->name, 'UTF-8')] = $city->term_id;
			}
		}

		// Отримуємо всі райони з таксономії property_city
		$districts = get_terms([
			'taxonomy' => 'property_city',
			'post_type' => 'estate_property',
			'hide_empty' => false
		]);

		if (!is_wp_error($districts) && !empty($districts)) {
			foreach ($districts as $district) {
				// Отримуємо дані про район з wp_options (як в 404.php)
				$district_data = get_option('taxonomy_' . $district->term_id);
				$parent_city_id = '';

				if (is_array($district_data) && isset($district_data['stateparent']) && !empty($district_data['stateparent'])) {
					$parent_city_name = mb_strtolower($district_data['stateparent'], 'UTF-8');
					if (isset($city_name_to_id_map[$parent_city_name])) {
						$parent_city_id = $city_name_to_id_map[$parent_city_name];
					}
				}

				// Пропускаємо райони без зв'язку з містом
				if (empty($parent_city_id)) {
					continue;
				}

				$data_parent_city = ' data-parent-city="' . esc_attr($parent_city_id) . '"';
				echo '<option value="' . esc_attr($district->slug) . '"' . $data_parent_city . '>' . esc_html($district->name) . '</option>';
			}
		}
		?>
	</select>
</div>
</div>
<div class="search-form__column select">
<label><?= esc_html__('Комнат', 'wpresidence-child'); ?></label>
<div class="wrap-field js-wrap-select-form wrap-select">
	<select id="search-rooms" name="rooms[]" class="select2-multi-checkboxes" multiple="multiple" data-placeholder="Все">
		<?php
		// Отримуємо значення кімнат з таксономії room
		$room_terms = get_terms([
			'taxonomy' => 'room',
			'hide_empty' => false,
			'orderby' => 'name',
			'order' => 'ASC',
		]);

		if (!empty($room_terms) && !is_wp_error($room_terms)) {
			foreach ($room_terms as $term) {
				echo '<option value="' . esc_attr($term->slug) . '">' . esc_html($term->name) . '</option>';
			}
		}
		?>
	</select>
</div>
</div>
<div class="search-form__column select price">
<label><?= esc_html__('Цена', 'wpresidence-child'); ?> EUR</label>
<div class="wrap-field price">
	<input type="number" name="price_from" placeholder="От">
	<span></span>
	<input type="number" name="price_to" placeholder="До">
</div>
</div>
<div class="search-form__column id">
<label>ID</label>
<div class="wrap-field">
	<input type="number" name="id" placeholder="Все">
</div>
</div>
    <button type="submit" class="search-form__submit"><?= __('НАЙТИ', 'wpresidence-child') ?></button>
<div class="search-form__column js-wrap-select-form select price extended-field">
<label><?= esc_html__('Площадь', 'wpresidence-child'); ?></label>
<div class="wrap-field price">
	<input type="number" name="square_from" placeholder="От">
	<span></span>
	<input type="number" name="square_to" placeholder="До">
</div>
</div>
<div class="search-form__column select price extended-field">
<label><?= esc_html__('Этаж', 'wpresidence-child'); ?></label>
<div class="wrap-field price">
	<input type="number" name="floor_from" placeholder="От">
	<span></span>
	<input type="number" name="floor_to" placeholder="До">
</div>
</div>
<div class="search-form__column select price extended-field">
<label><?= esc_html__('Год строительства', 'wpresidence-child'); ?></label>
<div class="wrap-field price">
	<input type="number" name="year_from" placeholder="От">
	<span></span>
	<input type="number" name="year_to" placeholder="До">
</div>
</div>
<div class="search-form__column select price extended-field">
<label><?= esc_html__('До моря', 'wpresidence-child'); ?></label>
<div class="wrap-field price">
	<input type="number" name="sea_from" placeholder="От">
	<span></span>
	<input type="number" name="sea_to" placeholder="До">
</div>
</div>
<div class="search-form__column select extended-field">
<label><?= __('Инфраструктура', 'wpresidence-child') ?></label>
<div class="wrap-field js-wrap-select-form wrap-select">
    <select id="search-infrastructure" name="infrastructure[]" class="select2-multi-checkboxes" multiple="multiple" data-placeholder="<?= __('Все', 'wpresidence-child') ?>">
		<?php
		$features = get_terms([
			'taxonomy' => 'property_features',
			'post_type' => 'estate_property',
			'hide_empty' => false
		]);
		if (!is_wp_error($features) && !empty($features) && is_array($features)) {
			foreach ($features as $fea) {
				echo '<option value="' . esc_attr($fea->slug) . '">' . esc_html($fea->name) . '</option>';
			}
		}
		?>
    </select>
</div>
</div>
<div class="search-form__column select extended-field">
<label><?= __('Преимущества', 'wpresidence-child') ?></label>
<div class="wrap-field js-wrap-select-form wrap-select">
	<select id="search-advantages" name="advantages[]" class="select2-multi-checkboxes" multiple="multiple" data-placeholder="<?= __('Все', 'wpresidence-child') ?>">
		<?php
		$advantages = get_terms([
			'taxonomy' => 'property_advantages',
			'post_type' => 'estate_property',
			'hide_empty' => false
		]);
		if (!is_wp_error($advantages) && !empty($advantages) && is_array($advantages)) {
			foreach ($advantages as $adv) {
				echo '<option value="' . esc_attr($adv->slug) . '">' . esc_html($adv->name) . '</option>';
			}
		}
		?>
	</select>
</div>
</div>
<!--Button-->
</div>
</form>
                <button type="button" class="advanced-search-button">
                    <span id="advanced-search" class="advanced"><?= __('Расширенный поиск', 'wpresidence-child') ?></span>
                    <span id="simple-search" class="simple"><?= __('Простой поиск', 'wpresidence-child') ?></span>
                </button>
			</div>
		</div>
	</div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function () {
    var tabs = document.querySelectorAll('.tabs__wrap-tab .tab');
    var countryInput = document.getElementById('search-country');
    tabs.forEach(function(tab) {
        tab.addEventListener('click', function() {
            var country = this.getAttribute('data-tab');
            // Змінюємо значення прихованого поля
            if (countryInput) countryInput.value = country;
            // Активуємо відповідну вкладку
            tabs.forEach(function(t) { t.classList.remove('tab-active'); });
            this.classList.add('tab-active');
        });
    });

    // Зберігаємо оригінальні опції для відновлення
    var originalDistrictOptions = null;

    // Додаємо функцію для фільтрації районів по вибраному місту
    function filterDistrictsByCity(selectedCityIds) {
        var districtSelect = document.getElementById('search-district');
        if (!districtSelect) {
            return;
        }

        // Зберігаємо оригінальні опції при першому виклику
        if (!originalDistrictOptions) {
            originalDistrictOptions = Array.from(districtSelect.querySelectorAll('option')).map(function(option) {
                return {
                    element: option.cloneNode(true),
                    parentCityId: option.getAttribute('data-parent-city'),
                    value: option.value,
                    text: option.textContent
                };
            });
        }

        // Зберігаємо поточні вибрані значення
        var currentValues = [];
        if (window.jQuery && jQuery(districtSelect).hasClass('select2-multi-checkboxes')) {
            currentValues = jQuery(districtSelect).val() || [];
        } else {
            currentValues = Array.from(districtSelect.selectedOptions).map(function(opt) { return opt.value; });
        }

        // Очищуємо select
        districtSelect.innerHTML = '';

        // Додаємо тільки потрібні опції
        originalDistrictOptions.forEach(function(optionData) {
            var parentCityId = optionData.parentCityId;

            if (!parentCityId || optionData.value === '') {
                // Опція без parent_city або порожня (наприклад, "Все") - завжди додаємо
                districtSelect.appendChild(optionData.element.cloneNode(true));
                return;
            }

            if (selectedCityIds.length === 0 || selectedCityIds.includes(parentCityId)) {
                districtSelect.appendChild(optionData.element.cloneNode(true));
            }
        });

        // Якщо використовується Select2, перебудовуємо його з оригінальною конфігурацією
        if (window.jQuery && jQuery(districtSelect).hasClass('select2-multi-checkboxes')) {
            var $select = jQuery(districtSelect);
            var placeholder = $select.attr('data-placeholder') || 'Все';

            // Знищуємо старий Select2
            $select.select2('destroy');

            // Відновлюємо клас для чекбоксів
            $select.addClass('select2-multi-checkboxes');

            // Ініціалізуємо Select2 з точно такою ж конфігурацією як в темі
            $select.select2({
                placeholder: placeholder,
                allowClear: true,
                closeOnSelect: false,
                width: '100%',
                templateResult: function(result) {
                    if (!result || !result.id) {
                        return result ? result.text : '';
                    }

                    // Check if this option is currently selected
                    var selectedValues = $select.val() || [];
                    var isSelected = selectedValues.indexOf(result.id) !== -1;

                    var $result = jQuery(
                        '<span class="select2-result-repository">' +
                            '<div class="select2-result-repository__meta">' +
                                '<div class="select2-result-repository__title">' +
                                    '<input type="checkbox" class="select2-result-repository__checkbox" ' +
                                    (isSelected ? 'checked' : '') + ' data-value="' + result.id + '" />' +
                                    '<span class="select2-result-repository__text">' + (result.text || '') + '</span>' +
                                '</div>' +
                            '</div>' +
                        '</span>'
                    );

                    $result.find('.select2-result-repository__checkbox').on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                    });

                    return $result;
                },
                templateSelection: function(selection) {
                    // Для multi-select просто повертаємо текст кожної опції окремо
                    if (!selection || !selection.id) {
                        return selection ? selection.text : '';
                    }

                    // Повертаємо просто текст опції без додаткової логіки
                    return selection.text || '';
                }
            });

            // Функція для оновлення відображення вибраних опцій
            function updateSelectionDisplay($select) {
                try {
                    var $container = $select.next('.select2-container');
                    var $rendered = $container.find('.select2-selection__rendered');
                    var $clearButton = $container.find('.select2-selection__clear');

                    // Get all selected options
                    var selectedOptions = $select.select2('data');

                    // Filter out the "All" option (empty value) from selected options
                    var realSelectedOptions = selectedOptions.filter(function(option) {
                        return option && option.id && option.id !== '';
                    });

                    if (realSelectedOptions.length === 0) {
                        // No real options selected, show placeholder as choice
                        $rendered.html('<li class="select2-selection__choice"><span class="select2-selection__choice__text">' + placeholder + '</span></li>');
                        $clearButton.hide();
                    } else if (realSelectedOptions.length === 1) {
                        // One option selected, show it normally
                        var firstOptionText = realSelectedOptions[0].text;
                        $rendered.html('<li class="select2-selection__choice"><span class="select-box-enable select-box-enable__title"><span class="select-box-enable">' + firstOptionText + '</span></span><span class="select2-selection__choice__remove" role="presentation">×</span></li>');
                        $clearButton.show();
                    } else {
                        // Multiple options selected, show first + count
                        var firstOptionText = realSelectedOptions[0].text;
                        $rendered.html('<li class="select2-selection__choice"><span class="select-box-enable select-box-enable__title"><span class="select-box-enable">' + firstOptionText + '</span> <span class="select-box-enable-num">(+' + (realSelectedOptions.length - 1) + ')</span></span><span class="select2-selection__choice__remove" role="presentation">×</span></li>');
                        $clearButton.show();
                    }
                } catch (e) {
                    // Silent error handling
                }
            }

            // Додаємо обробники подій як в оригінальній темі
            $select.on('select2:opening', function(e) {
                try {
                    // Refresh the dropdown to update checkbox states
                    setTimeout(function() {
                        var selectedValues = $select.val() || [];
                        jQuery('.select2-result-repository__checkbox').each(function() {
                            var $checkbox = jQuery(this);
                            var value = $checkbox.data('value');
                            if (selectedValues.indexOf(value) !== -1) {
                                $checkbox.prop('checked', true);
                            } else {
                                $checkbox.prop('checked', false);
                            }
                        });
                    }, 50);
                } catch (e) {
                    // Silent error handling
                }
            });

            // Оновлюємо відображення після зміни вибору
            $select.on('select2:select select2:unselect', function(e) {
                setTimeout(function() {
                    updateSelectionDisplay($select);
                }, 10);
            });

            // Відновлюємо вибрані значення (тільки ті що тепер доступні)
            var visibleValues = currentValues.filter(function(value) {
                var option = districtSelect.querySelector('option[value="' + value + '"]');
                return option !== null;
            });

            if (visibleValues.length > 0) {
                $select.val(visibleValues).trigger('change');
                // Оновлюємо відображення після відновлення значень
                setTimeout(function() {
                    updateSelectionDisplay($select);
                }, 50);
            } else {
                // Оновлюємо відображення навіть якщо немає значень
                setTimeout(function() {
                    updateSelectionDisplay($select);
                }, 50);
            }
        }
    }

    // Додаємо обробники зміни вибору міста (для звичайного select і Select2)
    var citySelect = document.getElementById('search-city');
    if (citySelect) {
        // Обробник для звичайного select
        citySelect.addEventListener('change', function() {
            handleCityChange();
        });

        // Обробник для Select2 (якщо використовується)
        if (window.jQuery && jQuery(citySelect).hasClass('select2-multi-checkboxes')) {
            jQuery(citySelect).on('change', function() {
                handleCityChange();
            });
        }

        function handleCityChange() {
            var selectedOptions = Array.from(citySelect.selectedOptions);
            var selectedCityIds = selectedOptions.map(function(option) {
                return option.getAttribute('data-city-id');
            }).filter(function(id) { return id; });

            filterDistrictsByCity(selectedCityIds);
        }

        // Ініціалізуємо фільтрацію при завантаженні сторінки
        // Перевіряємо чи є вже вибрані міста і застосовуємо фільтр
        setTimeout(function() {
            handleCityChange();
        }, 500);
    }
});
</script>
