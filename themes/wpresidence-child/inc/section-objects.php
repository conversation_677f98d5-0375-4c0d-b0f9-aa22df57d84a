<?php
// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>
<section class="section-objects">
	<div class="n_container">
		<div class="section-objects__wrapTitle">
			<div class="section-objects__left">
				<h3 class="testimonials__aboveTitle"><?php esc_html_e('OBJECTS', 'wpresidence-child'); ?></h3>
				<h2><?php esc_html_e('Вся недвижимость', 'wpresidence-child'); ?></h2>
			</div>
			<div class="section-objects__right">
				<p><?php esc_html_e('Вся недвижимость Турции — это разнообразие предложений на любой вкус: от уютных апартаментов у моря в Анталии до роскошных вилл на побережье Эгейского моря и современных квартир в центре Стамбула. Турецкая недвижимость сочетает в себе комфорт, качество и выгоду, делая её идеальным выбором как для отдыха, так и для постоянного проживания.', 'wpresidence-child'); ?></p>
			</div>
		</div>
		<div class="tabs-objects">
			<div class="tabs__scroll">
				<div class="tabs__wrap-tab">
					<?php
					// Get all property categories
					$property_categories = get_terms(array(
						'taxonomy' => 'property_category',
						'hide_empty' => false,
					));

					// Add "All Types" tab
					echo '<div data-tab="all-types" class="tab tab-active">' . esc_html__('All Types', 'wpresidence-child') . '</div>';

					// Output category tabs
					if (!empty($property_categories) && !is_wp_error($property_categories)) {
						foreach ($property_categories as $category) {
							printf(
								'<div data-tab="%s" class="tab">%s</div>',
								esc_attr($category->slug),
								esc_html($category->name)
							);
						}
					}
					?>
				</div>
			</div>

			<?php
			// All Types tab content
			?>
			<div data-tabcontent="all-types" class="tabContent content-active">
				<div id="objects-slider-1" class="newest-objects__slider swiper-conteiner">
					<div class="swiper-wrapper">
						<?php
						$args = array(
							'post_type' => 'estate_property',
							'posts_per_page' => 4,
							'orderby' => 'date',
							'order' => 'DESC',
						);

						$properties_query = new WP_Query($args);
						if ($properties_query->have_posts()) :
							while ($properties_query->have_posts()) : $properties_query->the_post();
								$city = strip_tags(get_the_term_list(get_the_ID(), 'property_city', '', ', ', ''));
								$square = get_post_meta(get_the_ID(), 'd0bfd0bbd0bed189d0b0d0b4d18c', true);
								$terms = get_the_terms(get_the_ID(), 'room'); // Терміни таксономії room
								if ($terms && !is_wp_error($terms)) {
									$room_names = wp_list_pluck($terms, 'name');
									$rooms = implode(', ', $room_names);
								} else {
									$rooms = '';
								}
								$to_sea = get_post_meta(get_the_ID(), 'd0b4d0be-d0bcd0bed180d18f', true);
								$price = get_post_meta(get_the_ID(), 'd186d0b5d0bdd0b0', true);
								$currency = get_post_meta(get_the_ID(), 'property_currency', true);
								$object_id = get_the_ID();
								?>
								<div class="newest-objects__object swiper-slide">
									<div class="object__blockImage">
										<div class="tags">
											<?php
											$tags = get_the_terms(get_the_ID(), 'property_action_category');
											if ($tags && !is_wp_error($tags)) {
												foreach ($tags as $tag) {
													$class = 'tag';
													if ($tag->name === 'Продажа') {
														$class .= ' vivid-orange';
													} elseif ($tag->name === 'Аренда') {
														$class .= ' vivid-red';
													}
													echo '<span class="' . esc_attr($class) . '">' . esc_html($tag->name) . '</span>';
												}
											}
											?>
										</div>
										<a href="<?php the_permalink(); ?>">
											<?php if (has_post_thumbnail()) : ?>
												<img loading="lazy" src="<?php the_post_thumbnail_url('large'); ?>" alt="<?php the_title_attribute(); ?>">
											<?php else : ?>
												<img loading="lazy" src="<?php echo esc_url(get_stylesheet_directory_uri()); ?>/html/images/photo-object-default.png" alt="Default Image">
											<?php endif; ?>
										</a>
										<p class="object__id">ID: <?php echo esc_html($object_id); ?></p>
									</div>
									<h3 class="object__name"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
									<ul class="object__list">
										<li class="location-icon"><?= __('Расположение', 'wpresidence-child'); ?>: <strong><?php echo esc_html($city ? $city : '-'); ?></strong></li>
										<li class="square-icon"><?= __('Площадь', 'wpresidence-child'); ?>: <strong><?php echo esc_html($square ? $square : '-'); ?></strong></li>
										<li class="rooms-icon"><?= __('Комнат', 'wpresidence-child'); ?>: <strong><?php echo esc_html($rooms ? $rooms : '-'); ?></strong></li>
										<li class="to-sea-icon"><?= __('До моря', 'wpresidence-child'); ?>: <strong><?php echo esc_html($to_sea ? $to_sea : '-'); ?></strong></li>
									</ul>
									<a href="<?php the_permalink(); ?>" class="object__button"><span class="currency"><?php echo esc_html($currency ? $currency : '€'); ?></span> <?php echo esc_html($price ? $price : '-'); ?></a>
								</div>
							<?php
							endwhile;
							wp_reset_postdata();
						else :
							?>
							<p><?php esc_html_e('No properties found.', 'wpresidence-child'); ?></p>
						<?php
						endif;
						?>
					</div>
					<div id="objects-pagination-slider-1" class="swiper-pagination square-dots"></div>
				</div>
			</div>

			<?php
			// Category tabs content
			if (!empty($property_categories) && !is_wp_error($property_categories)) {
				$slider_counter = 2;
				foreach ($property_categories as $category) {
					?>
					<div data-tabcontent="<?php echo esc_attr($category->slug); ?>" class="tabContent">
						<div id="objects-slider-<?php echo esc_attr($slider_counter); ?>" class="newest-objects__slider swiper-conteiner">
							<div class="swiper-wrapper">
								<?php
								$args = array(
									'post_type' => 'estate_property',
									'posts_per_page' => 4,
									'orderby' => 'date',
									'order' => 'DESC',
									'tax_query' => array(
										array(
											'taxonomy' => 'property_category',
											'field' => 'term_id',
											'terms' => $category->term_id,
										),
									),
								);

								$properties_query = new WP_Query($args);
								if ($properties_query->have_posts()) :
									while ($properties_query->have_posts()) : $properties_query->the_post();
										$city = strip_tags(get_the_term_list(get_the_ID(), 'property_city', '', ', ', ''));
										$square = get_post_meta(get_the_ID(), 'd0bfd0bbd0bed189d0b0d0b4d18c', true);
										$terms = get_the_terms(get_the_ID(), 'room'); // Терміни таксономії room
										if ($terms && !is_wp_error($terms)) {
											$room_names = wp_list_pluck($terms, 'name');
											$rooms = implode(', ', $room_names);
										} else {
											$rooms = '';
										}
										$to_sea = get_post_meta(get_the_ID(), 'd0b4d0be-d0bcd0bed180d18f', true);
										$price = get_post_meta(get_the_ID(), 'd186d0b5d0bdd0b0', true);
										$currency = get_post_meta(get_the_ID(), 'property_currency', true);
										$object_id = get_the_ID();
										?>
										<div class="newest-objects__object swiper-slide">
											<div class="object__blockImage">
												<div class="tags">
													<?php
													$tags = get_the_terms(get_the_ID(), 'property_action_category');
													if ($tags && !is_wp_error($tags)) {
														foreach ($tags as $tag) {
															$class = 'tag';
															if ($tag->name === 'Продажа') {
																$class .= ' vivid-orange';
															} elseif ($tag->name === 'Аренда') {
																$class .= ' vivid-red';
															}
															echo '<span class="' . esc_attr($class) . '">' . esc_html($tag->name) . '</span>';
														}
													}
													?>
												</div>
												<a href="<?php the_permalink(); ?>">
													<?php if (has_post_thumbnail()) : ?>
														<img loading="lazy" src="<?php the_post_thumbnail_url('large'); ?>" alt="<?php the_title_attribute(); ?>">
													<?php else : ?>
														<img loading="lazy" src="<?php echo esc_url(get_stylesheet_directory_uri()); ?>/html/images/photo-object-default.png" alt="Default Image">
													<?php endif; ?>
												</a>
												<p class="object__id">ID: <?php echo esc_html($object_id); ?></p>
											</div>
											<h3 class="object__name"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
											<ul class="object__list">
												<li class="location-icon"><?= __('Расположение', 'wpresidence-child'); ?>: <strong><?php echo esc_html($city ? $city : '-'); ?></strong></li>
												<li class="square-icon"><?= __('Площадь', 'wpresidence-child'); ?>: <strong><?php echo esc_html($square ? $square : '-'); ?></strong></li>
												<li class="rooms-icon"><?= __('Комнат', 'wpresidence-child'); ?>: <strong><?php echo esc_html($rooms ? $rooms : '-'); ?></strong></li>
												<li class="to-sea-icon"><?= __('До моря', 'wpresidence-child'); ?>: <strong><?php echo esc_html($to_sea ? $to_sea : '-'); ?></strong></li>
											</ul>
											<a href="<?php the_permalink(); ?>" class="object__button"><span class="currency"><?php echo esc_html($currency ? $currency : '€'); ?></span> <?php echo esc_html($price ? $price : '-'); ?></a>
										</div>
									<?php
									endwhile;
									wp_reset_postdata();
								else :
									?>
									<p><?php esc_html_e('No properties found in this category.', 'wpresidence-child'); ?></p>
								<?php
								endif;
								?>
							</div>
							<div id="objects-pagination-slider-<?php echo esc_attr($slider_counter); ?>" class="swiper-pagination square-dots"></div>
						</div>
					</div>
					<?php
					$slider_counter++;
				}
			}
			?>
		</div>
		<div class="section-objects__bottom">
			<a href="<?php echo esc_url(get_post_type_archive_link('estate_property')); ?>" class="section-objects__button"><?php esc_html_e('Смотреть все', 'wpresidence-child'); ?></a>
		</div>
	</div>
</section>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        var tabsBlocks = document.querySelectorAll('.tabs-objects');

        tabsBlocks.forEach(function (tabsBlock) {
            var tabs = tabsBlock.querySelectorAll('.tabs__wrap-tab .tab');
            var contents = tabsBlock.querySelectorAll('.tabContent');

            tabs.forEach(function (tab) {
                tab.addEventListener('click', function () {
                    var tabId = this.getAttribute('data-tab');
                    var targetContent = tabsBlock.querySelector('.tabContent[data-tabcontent="' + tabId + '"]');

                    tabs.forEach(function (t) {
                        t.classList.remove('tab-active');
                    });

                    contents.forEach(function (c) {
                        c.classList.remove('content-active');
                    });

                    this.classList.add('tab-active');
                    if (targetContent) {
                        targetContent.classList.add('content-active');
                    }
                });
            });
        });
    });
</script>