@charset "UTF-8";

/* Fonts */

@font-face {
  font-family: "<PERSON>roy";
  src: url("../files/fonts/gilroy/<PERSON><PERSON>-Regular.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "<PERSON>roy";
  src: url("../files/fonts/gilroy/<PERSON>roy-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Gilroy";
  src: url("../files/fonts/gilroy/<PERSON>roy-Semibold.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: "Gilroy";
  src: url("../files/fonts/gilroy/<PERSON>roy-Bold.woff2") format("woff2");
  font-weight: bold;
  font-style: normal;
}

/* Normalize */

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

button {
  background: none;
  border: none;
  cursor: pointer;
}

picture,
img {
  height: auto;
  max-width: 100%;
  vertical-align: top;
}

/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */

/* Document
   ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */

html {
  line-height: 1.15;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}

/* Sections
   ========================================================================== */

/**
 * Remove the margin in all browsers.
 */

body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */

main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */

hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/* Text-level semantics
   ========================================================================== */

/**
 * Remove the gray background on active links in IE 10.
 */

a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */

abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  /* 2 */
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */

b,
strong {
  font-weight: 700;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

code,
kbd,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/**
 * Add the correct font size in all browsers.
 */

small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */

/**
 * Remove the border on images inside links in IE 10.
 */

img {
  border-style: none;
}

/* Forms
   ========================================================================== */

/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: 1.15;
  /* 1 */
  margin: 0;
  /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */

button,
input {
  /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */

button,
select {
  /* 1 */
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */

button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */

button:-moz-focusring,
[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */

fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */

legend {
  box-sizing: border-box;
  /* 1 */
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */

progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */

textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */

[type=checkbox],
[type=radio] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */

[type=search] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */

[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/* Interactive
   ========================================================================== */

/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */

details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */

summary {
  display: list-item;
}

/* Misc
   ========================================================================== */

/**
 * Add the correct display in IE 10+.
 */

template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */

[hidden] {
  display: none;
}

/* Mixins */

/* Максимальная ширина контейнера с контентом */

/* Media screen */

/*
@media screen and (max-width: 960px) and (orientation: landscape) {
}

@media screen and (max-width: 960px) and (orientation: portrait) {
}
*/

/* ui */

html,
body {
  height: 100%;
}

.wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.wrapper.page-not-front .header__wrapper {
  background-color: #fff;
  position: sticky;
}

.wrapper.page-not-front .block-search-form .tabs {
  margin-top: 50px;
}

main.main {
  flex: 1;
}

body {
  background-color: #F4F4F4;
  color: #2A2A29;
  font-family: "Gilroy", sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%;
  letter-spacing: 0;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 0;
}

body._lock {
  overflow: hidden;
}

body ul li,
body ol li {
  font-size: inherit;
  line-height: inherit;
}

body p:not(:last-child) {
  margin-bottom: 20px;
}

body strong {
  font-weight: 700;
}

.breadcrumbs {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 52px;
  padding-top: 12px;
  position: relative;
}

.breadcrumbs__item {
  font-size: 12px;
  font-weight: 500;
  line-height: 120%;
  list-style: none;
  margin-bottom: 3px;
  margin-top: 3px;
  position: relative;
  text-align: left;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.breadcrumbs__item:not(:last-child)::after {
  align-items: center;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='4' height='7' viewBox='0 0 4 7' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M1 6L3.5 3.5L1 1' stroke='%2300B6DA' stroke-width='0.714286' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  bottom: 4px;
  content: "";
  height: 7px;
  display: inline-flex;
  justify-content: center;
  margin: 0 15px 0 11px;
  position: relative;
  width: 4px;
}

.breadcrumbs__link {
  color: #8E90A0;
  position: relative;
  text-decoration: none;
}

.n_container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1320px;
  padding-left: 40px;
  padding-right: 40px;
  width: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 45px;
  line-height: 120%;
  font-weight: 600;
}

h1 span,
h2 span,
h3 span,
h4 span,
h5 span,
h6 span {
  color: #00B6DA;
}

a {
  color: inherit;
}

/*
.page_title,
.page-title,
.page__title {
	font-size: 64px;
	font-weight: 400;
	line-height: 100%;
	margin: 2px 0 0;

	@include media-max-1100 {
		font-size: 50px;
		line-height: 120%;
	}
	@include media-max-575 {
		font-size: 24px;
	}
}
*/

/*
.n_sec-title {
	font-size: 35px;
	line-height: 116%;
	text-transform: uppercase;

	@include media-max-1024 {
		font-size: 28px;
	}
	@include media-max-575 {
		font-size: 20px;
	}

	.lg-color {
		background: linear-gradient(89.8deg, #0A6F6D 1.02%, #1BCDCA 45.26%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	&.quote {


		.lg-color {

			div {

				span {
					background: linear-gradient(89.8deg, #0A6F6D 1.02%, #1BCDCA 45.26%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}

	}
}
*/

/*
.block {

   &__item {
		border: 1px solid #ccc;

		&:not(:last-child) {
			margin-bottom: 30px;
		}
   }

	&__text {
		overflow: hidden;
		padding: 0 29px 35px;

		ol,
		ul {
			margin-top: 35px;

			li {
				@include adaptive-value("font-size", 20, 19, 1);
				@include adaptive-value("line-height", 30, 29, 1);
				padding-left: 46px;
				position: relative;

				&:not(:last-child) {
					margin-bottom: 12px;
				}

				&::before {
					content: url("data:image/svg+xml;charset=UTF-8,%3csvg width='31' height='16' viewBox='0 0 31 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M30.7071 8.70711C31.0976 8.31658 31.0976 7.68342 30.7071 7.29289L24.3431 0.928932C23.9526 0.538408 23.3195 0.538408 22.9289 0.928932C22.5384 1.31946 22.5384 1.95262 22.9289 2.34315L28.5858 8L22.9289 13.6569C22.5384 14.0474 22.5384 14.6805 22.9289 15.0711C23.3195 15.4616 23.9526 15.4616 24.3431 15.0711L30.7071 8.70711ZM0 9H30V7H0V9Z' fill='%2321C677'/%3e%3c/svg%3e");
					margin-right: 15px;
					left: 0;
					position: absolute;
					top: 1px;
				}
			}
		}

		p {
			font-size: 18px;
			line-height: 27px;

			&:first-child {
				margin-top: 29px;
			}

			&:not(:last-child) {
				margin-bottom: 18px;
			}
		}
	}

	&__title {
		background: none;
		display: flex;
		justify-content: space-between;
		font-family: 'Prosto One';
		@include adaptive-value("font-size", 26, 20, 1);
		font-weight: 400;
      @include adaptive-value("line-height", 34, 26, 1);
		padding: 22px 28px;
		position: relative;
		text-align: left;
		width: 100%;

		&::after {
			background-color: #000;
			bottom: 0;
			content: "";
			display: block;
			height: 1px;
			left: 29px;
			opacity: 0;
			position: absolute;
			right: 29px;
			transition: 0.3s;
			width: auto;
		}

		@media (any-hover: hover) {
			&:hover {
				cursor: pointer;
			}
		}

		&._active {

			.block__title-cross {
				border-color: #21C677;

				&:before {
					background-color: #21C677;
				}
				&::after {
					height: 0;
				}
			}

			&::after {
				opacity: 1;
			}
		}

		&-cross {
			border: 1.5px solid #000;
			border-radius: 50%;
			display: block;
			height: 30px;
			font-size: 0;
			margin-left: 12px;
			position: relative;
			transition: 0.3s;
			top: 2px;
			min-width: 30px;

			&::before,
			&::after {
				background-color: #000;
				content: "";
				position: absolute;
				transition: 0.3s;
				width: 14px;
			}
			&::before {
				left: 50%;
				height: 1.5px;
				transform: translate(-50%, -50%);
				top: 50%;
				width: 14px;
			}
			&::after {
				left: 50%;
				height: 14px;
				transform: translate(-50%, -50%);
				top: 50%;
				width: 1.5px;
			}
		}
	}
}
.faq {
	padding-bottom: 183px;
	padding-top: 90px;

	@include media-max-1100 {
		padding-bottom: 120px;
		padding-top: 60px;
	}
	@include media-max-575 {
		padding-bottom: 105px;
      padding-top: 16px;
	}

	.block-with-two-columns__title {
		margin-bottom: 74px;

		@include media-max-575 {
			margin-bottom: 55px;
		}
	}

	.block {

		&__item {

			&:not(:last-child) {
				margin-bottom: 66px !important;

				@include media-max-1100 {
					margin-bottom: 50px !important;
				}
				@include media-max-960 {
					margin-bottom: 34px !important;
				}
			}
		}

		&__title {
			background: #FAFAFA !important;
			font-family: "Montserrat", sans-serif;
			font-size: 36px;
			font-weight: 600;
			line-height: 1.3;
			padding: 38px 85px 39px 47px;
			position: relative;
			transition: 0.3s ease 0s;

			@include media-max-1100 {
				font-size: 32px;
			}
			@include media-max-960 {
				font-size: 20px;
				padding: 30px 65px 31px 30px;
			}
			@include media-max-575 {
				font-size: 16px;
				line-height: 1.26;
				min-height: 62px;
				padding: 11px 45px 10px 25px;
			}

			&._active {
				background: #D9D9D9 !important;

				&::before,
				&::after {
					background: #FEFEFE !important;
					transform: rotate(45deg) !important;
				}

				&::before {
					height: 22px;
					right: 63px;
					top: calc(50% - 12px) !important;
					width: 2px;

					@include media-max-960 {
						right: 39px;
						top: calc(50% - 12px) !important;
					}
					@include media-max-575 {
						height: 19px;
						right: 25px;
						top: calc(50% - 10px) !important;
						width: 1px;
					}
				}
				&::after {
					height: 2px !important;
					top: calc(50% - 2px) !important;
					width: 22px !important;

					@include media-max-960 {
						right: 29px !important;
						top: calc(50% - 2px) !important;
					}
					@include media-max-575 {
						height: 1px !important;
						right: 16px !important;
						top: calc(50% - 1px) !important;
						width: 19px !important;
					}
				}
			}

			&::before,
			&::after {
				background-color: #000;
				content: '';
				display: block;
				position: absolute;
				top: calc(50% - 2px) !important;
				transition: 0s ease 0s !important;
				transform: translateY(-50%) !important;

				@include media-max-960 {
					top: 50% !important;
				}
			}
			&::before {
				height: 18px;
				right: 60px;
				width: 4px;

				@include media-max-960 {
					right: 38px;
				}
				@include media-max-575 {
					height: 8px;
					right: 20px;
					width: 2px;
				}
			}
			&::after {
				height: 4px !important;
				right: 53px !important;
				width: 18px !important;

				@include media-max-960 {
					right: 31px !important;
				}
				@include media-max-575 {
					height: 2px !important;
					right: 17px !important;
					width: 8px !important;
				}
			}
		}

		&__content {
    		padding: 62px 0 0;

			@include media-max-1100 {
				padding-top: 50px;
			}
			@include media-max-960 {
				padding-top: 35px;
			}
			@include media-max-575 {
				padding-top: 26px;
			}

			p,
			li {
				font-family: Montserrat, sans-serif;
				font-size: 36px;
				font-weight: 400;
				line-height: 1.3;

				@include media-max-1100 {
					font-size: 32px;
				}
				@include media-max-960 {
					font-size: 20px;
				}
				@include media-max-575 {
					font-size: 16px;
					line-height: 1.21;
				}
			}

			p {
				margin-bottom: 35px !important;

				@include media-max-1100 {
					margin-bottom: 31px !important;
				}
				@include media-max-960 {
					margin-bottom: 19px !important;
				}
				@include media-max-575 {
					margin-bottom: 15px !important;
				}
			}

			ol,
			ul {
				margin-bottom: 35px;

				@include media-max-1100 {
					margin-bottom: 31px;
				}
				@include media-max-960 {
					margin-bottom: 19px;
				}
				@include media-max-575 {
					margin-bottom: 15px;
				}
			}

			ol {
				padding-left: 59px;

				@include media-max-960 {
					padding-left: 35px;
				}
				@include media-max-575 {
					padding-left: 28px;
				}
			}

			ul {
				padding-left: 10px;

				li {
					padding-left: 30px;

					@include media-max-960 {
						padding-left: 22px;
					}
					@include media-max-575 {
						padding-left: 18px;
					}

					&::before {
						height: 7px;
						top: 20px;
						width: 7px;

						@include media-max-1100 {
							top: 18px;
						}
						@include media-max-960 {
							height: 5px;
							left: 2px;
							top: 11px;
							width: 5px;
						}
						@include media-max-575 {
							height: 4px;
							left: 0;
							top: 8px;
							width: 4px;
						}
					}
				}
			}
		}
	}
}
*/

/*
.page__content {

	.wp-caption {
		max-width: 100%;

		&:first-child {
			margin-top: 63px;

			@include media-max-1440 {
				margin-top: 40px;
			}
			@include media-max-1100 {
				margin-top: 25px;
			}
		}
		&:not(:first-child) {
			margin-top: 120px;

			@include media-max-1440 {
				margin-top: 80px;
			}
			@include media-max-1100 {
				margin-top: 50px;
			}
			@include media-max-575 {
				margin-top: 30px;
			}
		}

		.size-full {
			left: 0;
			height: auto;
			width: 100%;
		}

		.wp-caption-text {
			color: #616161;
			font-size: 16px;
			font-weight: 300;
			line-height: 120%;
			margin: 17px 0 57px !important;

			@include media-max-1100 {
				margin: 15px 0 45px !important;
			}
			@include media-max-720 {
				font-size: 14px; 
			}
			@include media-max-575 {
				font-size: 10px; 
				margin: 12px 0 0 !important;
			}
		}
	}

	p {
		font-size: 40px;
		font-weight: 400;
		line-height: 120%;
		margin-bottom: 72px !important;
		margin-top: 53px !important;

		@include media-max-1440 {
			font-size: 32px;
			margin-bottom: 45px !important;
			margin-top: 45px !important;
		}
		@include media-max-1100 {
			font-size: 24px;
			margin-bottom: 36px !important;
			margin-top: 36px !important;
		}
		@include media-max-960 {
			font-size: 18px;
		}
		@include media-max-720 {
			font-size: 16px;
		}
		@include media-max-575 {
			font-size: 14px;
		}

		strong {
			font-weight: 500;
		}
	}
}
*/

/*
.custom-checkbox {
	display: block;
	margin-bottom: 1em;
	padding-left: 35px;
	position: relative;
	user-select: none;
}
*/

/*.custom-checkbox__input {*/

/*clip: rect(0 0 0 0);*/

/*height: 1px;
	position: absolute;
	overflow: hidden;
	width: 1px;
}
*/

/*
.custom-checkbox__box {
	background-color: #fff;
	border: 1px solid #ccc;
	border-radius: 5px;
	height: 18px;
	margin-left: -35px;
	margin-top: 1px;
	position: absolute;
	top: -2px;
	width: 18px;
}
	*/

/* Checked */

/*
.custom-checkbox__input:checked + .custom-checkbox__box {
	background-color: #E8A961;
	border: 1px solid #E8A961;
	background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg version='1.0' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1280.000000 1253.000000' preserveAspectRatio='xMidYMid meet'%3e%3cg transform='translate(0.000000,1253.000000) scale(0.100000,-0.100000)' fill='%23fff' stroke='none'%3e%3cpath d='M12000 12520 c-192 -27 -395 -98 -655 -230 -446 -225 -961 -590 -1620 -1149 -926 -785 -2158 -2000 -3466 -3416 -661 -717 -1389 -1537 -1913 -2157 -87 -104 -159 -188 -160 -188 -1 0 -67 34 -146 76 -255 134 -875 442 -1078 536 -849 391 -1441 595 -1912 659 -134 18 -368 13 -479 -10 -224 -48 -405 -181 -495 -366 -52 -104 -69 -183 -70 -310 -1 -105 1 -113 32 -177 29 -59 55 -87 235 -254 438 -407 919 -904 1273 -1315 792 -921 1625 -2167 2579 -3858 168 -297 234 -355 405 -355 66 0 96 5 132 21 113 51 158 111 238 313 135 341 398 964 565 1335 1285 2866 2820 5295 4654 7365 520 587 971 1049 1821 1866 665 640 790 788 844 1005 81 328 -187 601 -604 614 -63 2 -144 0 -180 -5z'/%3e%3c/g%3e%3c/svg%3e");
	//background: #00b6da url("data:image/svg+xml;charset=UTF-8,%3csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 548.873 548.873' style='enable-background:new 0 0 548.873 548.873;' xml:space='preserve'%3e%3cpolygon points='449.34,47.966 195.46,301.845 99.533,205.917 0,305.449 95.928,401.378 195.46,500.907 294.99,401.378 548.873,147.496 ' fill='white'%3e%3c/polygon%3e%3c/svg%3e");
	background-size: 65%;
	background-repeat: no-repeat;
	background-position: 3px 3px;
}
*/

/*.custom-checkbox.checked .custom-checkbox-text {
	font-weight: 500;
}*/

/* Focus */

/*.custom-checkbox__input:focus + .custom-checkbox__box,
.custom-checkbox__input:checked:focus + .custom-checkbox__box {
	box-shadow: 0 0 0 2px #1a7aea, 0 0 0 4px #F3AC04;
}*/

/* Disabled */

/*.custom-checkbox__input:disabled + .custom-checkbox__box,
.custom-checkbox__input:checked:disabled + .custom-checkbox__box {
	opacity: 0.6;
}*/

/* n Select */

/* n Lazy Loading */

/* n Spollers */

.block {
  border-top: 1px solid #D4DADF;
  width: 100%;
}

.block__item {
  border-bottom: 1px solid #D4DADF;
}

.block__title {
  background: transparent !important;
  border: none;
  font-size: 18px;
  font-weight: 600;
  line-height: 150%;
  margin: 0 0 0 0 !important;
  padding: 19px 0 23px;
  position: relative;
  text-align: left;
  width: 100%;
}

.block__wrap-arrow {
  display: none;
}

.block__content {
  padding-bottom: 25px;
}

.block__content p,
.block__content li {
  color: #59656F;
  line-height: 150%;
}

.block__content p {
  margin-bottom: 20px !important;
}

.block__content p:last-child {
  margin-bottom: 0 !important;
}

.block__content p a {
  text-decoration: none;
}

.block__content ul,
.block__content ol {
  margin-bottom: 20px;
}

.block__content ul li:not(:last-child),
.block__content ol li:not(:last-child) {
  margin-bottom: 6px;
}

.block__content ol {
  padding-left: 28px;
}

.block__content ul li {
  list-style: none;
  list-style-position: inside;
  padding-left: 32px;
  position: relative;
}

.block__content ul li::before {
  background-color: #59656F;
  border-radius: 100px;
  content: "";
  display: block;
  height: 5px;
  left: 12px;
  position: absolute;
  top: 9px;
  width: 5px;
}

.block__content ol li {
  padding-left: 1px;
}

.block._init .block__title {
  cursor: pointer;
  padding-right: 45px;
  position: relative;
  /*

  &::after {
     background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='16' height='9' viewBox='0 0 16 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M8.70711 8.70711C8.31658 9.09763 7.68342 9.09763 7.29289 8.70711L0.928932 2.34315C0.538408 1.95262 0.538408 1.31946 0.928932 0.928932C1.31946 0.538408 1.95262 0.538408 2.34315 0.928932L8 6.58579L13.6569 0.928932C14.0474 0.538408 14.6805 0.538408 15.0711 0.928932C15.4616 1.31946 15.4616 1.95262 15.0711 2.34315L8.70711 8.70711ZM8 7H9V8H8H7V7H8Z' fill='black'/%3e%3c/svg%3e");
     background-repeat: no-repeat;
     background-position: center;
     content: '';
     height: 16px;
     position: absolute;
     right: 0;
     transition: transform 0.3s ease 0s;
     top: 6px;
     width: 16px;

     @include media-max-1100 {
        top: 5px;
     }
     @include media-max-960 {
        top: 6px;
     }
     @include media-max-575 {
        top: 1px;
     }
  }
  &._active:after {
     top: 7px;
     transform: scale(1, -1);

     @include media-max-1100 {
        top: 5px;
     }
     @include media-max-960 {
        top: 7px;
     }
     @include media-max-575 {
        top: 2px;
     }
  }
  &._active {

     span::after {
        opacity: 1;
     }
  }
  span {
     position: relative;

     &::after {

        @include media-max-575 {
           background-color: #1F1F1F;
           bottom: 0;
           content: "";
           height: 1px;
           display: table;
           opacity: 0;
           position: absolute;
           left: 0;
           right: 0;
           transition: 0.3s ease 0s;
           width: 100%;
        }
     }
  }
  */
  /*&:before,
  &:after {
     content: "";
     height: 2px;
     position: absolute;
     left: 0;
     transition: all 0.3s ease 0s;
     top: 11px;
     width: 12px;
  }
  &:after {
     transform: rotate(-90deg);
  }
  &._active:after {
     transform: rotate(0deg);
  }
  &._active:before,
  &._active:after {
     transform: rotate(180deg);
  }*/
}

.block._init .block__title:not(._active) .block__wrap-arrow {
  top: 19px;
}

.block._init .block__title:not(._active) .block__wrap-arrow svg {
  transform: scale(1, -1) translate(-50%, 50%);
}

.block._init .block__title:not(._active) .block__wrap-arrow svg path {
  stroke: #2a2a29;
}

.block._init .block__wrap-arrow {
  display: block;
  height: 32px;
  position: absolute;
  right: 0;
  transition: 0.3s ease 0s;
  top: 22px;
  width: 32px;
}

.block._init .block__wrap-arrow svg {
  left: 50%;
  position: absolute;
  top: 50%;
  transition: inherit;
  transform: translate(-50%, -50%);
}

.block._init .block__wrap-arrow svg path {
  transition: inherit;
}

/* n Pop up */

body.lock {
  overflow: hidden;
}

.popup {
  background: rgba(55, 58, 79, 0.85);
  height: 100%;
  left: 0;
  opacity: 0;
  position: fixed;
  transition: all 0.6s ease 0s;
  top: 0;
  overflow-y: auto;
  overflow-x: hidden;
  visibility: hidden;
  width: 100%;
  z-index: 9999;
}

.popup.open {
  opacity: 1;
  visibility: visible;
}

.popup__body {
  align-items: center;
  display: flex;
  justify-content: center;
  min-height: 100vh;
  padding-bottom: 40px;
  padding-right: 40px;
  padding-left: 40px;
  padding-top: 40px;
}

.popup__content {
  background-color: #fff;
  padding: 46px 50px 50px;
  position: relative;
  width: 708px;
  max-width: 100%;
}

.popup__content h2 {
  font-size: 45px;
  font-weight: 600;
  line-height: 120%;
  margin: 0 35px 27px 0;
}

.popup__content h2 span {
  color: #00B6DA;
}

.popup__content p {
  color: #59656F;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
  max-width: 483px;
  margin: 0 !important;
}

.popup__content-image {
  padding: 0 !important;
}

.popup__content-image img {
  max-width: 100%;
  vertical-align: top;
}

.popup__close {
  align-items: center;
  height: 28px;
  display: flex;
  justify-content: center;
  position: absolute;
  right: 50px;
  top: 49px;
  width: 28px;
  z-index: 2;
}

.popup__close svg,
.popup__close img {
  max-height: 100%;
  max-width: 100%;
}

.popup-video .popup__content {
  height: 392px;
  width: 695px;
  position: relative;
}

.popup-video .popup__content iframe {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  position: absolute;
  top: 0;
  width: 100%;
}

.popup-custom-checkbox {
  cursor: pointer;
  display: table;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin: 21px 0 0;
  padding-left: 23px;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.popup-custom-checkbox input {
  height: 15px;
  left: 0;
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  width: 15px;
}

.popup-custom-checkbox input:checked::before {
  border-color: #00b6da;
  background: #00b6da url("data:image/svg+xml;charset=UTF-8,%3csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 548.873 548.873' style='enable-background:new 0 0 548.873 548.873;' xml:space='preserve'%3e%3cpolygon points='449.34,47.966 195.46,301.845 99.533,205.917 0,305.449 95.928,401.378 195.46,500.907 294.99,401.378 548.873,147.496 ' fill='white'%3e%3c/polygon%3e%3c/svg%3e");
  background-size: 10px;
  background-repeat: no-repeat;
  background-position: 2px 2px;
}

.popup-custom-checkbox input::before {
  background-color: #fff;
  border: 1px solid #8E90A0;
  content: "";
  height: 16px;
  left: -1px;
  outline: 4px solid #fff;
  pointer-events: none;
  position: absolute;
  top: calc(50% - 1px);
  transform: translateY(-50%);
  width: 16px;
  z-index: 2;
}

/* n Classes */

/* Header */

.header {
  left: 0;
  position: sticky;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 9999;
  /*
  &.n_hidden {

  	.header__wrapper {
  		transform: translateY(-110%);
  	}
  }
  */
  /*&__burger {
  	align-items: center;
  	background: rgba(144, 145, 156, 0.06);
  	border-radius: 50%;
  	display: flex;
  	justify-content: center;
  	margin-right: 15px;

  	@include media-min-1580 {
  		height: 56px;
  		flex: 0 0 56px;
  	}
  	@include media-max-1579 {
  		height: 45px;
  		flex: 0 0 45px;
  	}
  	@include media-max-991 {
  		height: 38px;
  		flex: 0 0 38px;
  	}

  	@media (any-hover: hover) {

  		&:hover {

  			.header__burger-line::after {
  				flex: 0 0 22px;
  			}
  		}
  	}

  	&-line {
  		background-color: #fff;
  		font-size: 0;
  		height: 2px;
  		margin-top: -6px;
  		flex: 0 0 22px;

  		@include media-max-991 {
  			margin-top: -5px;
  			flex: 0 0 18px;
  		}

  		&::after {
  			background-color: inherit;
  			content: "";
  			display: block;
  			height: inherit;
  			margin-top: 6px;
  			transition: width 0.3s;
  			flex: 0 0 17px;

  			@include media-max-991 {
  				margin-top: 5px;
  				flex: 0 0 13px;
  			}
  		}
  	}
  }*/
  /*
  &__close-menu {
  	background: none;
  	display: block;
  	height: 44px;
  	position: relative;
  	width: 44px;

  	@include media-min-1100 {
  		display: none;
  	}
  }
  */
}

.header.show-submenu .header__wrapper {
  background-color: #fff;
}

.header.n_scroll .header__wrapper {
  background-color: #fff;
  box-shadow: 0 25px 25px rgba(0, 0, 0, 0.03);
}

.header__wrapper {
  background-color: transparent;
  left: 0;
  position: fixed;
  transition: 0.3s ease 0s;
  top: 0;
  width: 100%;
  z-index: 50;
}

.header-container {
  max-width: 1320px;
  padding: 12px 40px 16px;
  position: relative;
  width: 100%;
}

.header__top {
  align-items: center;
  display: flex;
  padding-bottom: 1px;
  width: 100%;
}

.header__wrap-social-and-phone {
  align-items: center;
  display: flex;
  margin-left: auto;
}

.header__social {
  display: flex;
  flex-wrap: wrap;
}

.header__social li {
  height: 16px;
  list-style: none;
  margin: 6px;
  width: 16px;
}

.header__social li a {
  display: block;
  height: 100%;
  width: 100%;
}

.header__social li.ws-icon a {
  background: url("../images/svg/ws-header-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.header__social li.tg-icon a {
  background: url("../images/svg/tg-header-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.header__social li.vb-icon a {
  background: url("../images/svg/vb-header-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.header__phone {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  text-decoration: none;
}

.header__bottom {
  align-items: center;
  display: flex;
  width: 100%;
}

.header__blockLogo,
.header__btn-menu {
  position: relative;
  z-index: 6;
}

.header__blockLogo {
  min-width: 160px;
  max-width: 160px;
  margin-right: 20px;
}

.header__blockLogo img {
  max-height: 100%;
  max-width: 100%;
}

.header__icons {
  align-items: center;
  display: flex;
  margin-left: 89px;
}

.header__icons li {
  list-style: none;
  height: 20px;
  margin: 10px;
  width: 20px;
}

.header__icons li img,
.header__icons li svg {
  max-height: 100%;
  max-width: 100%;
}

.header__blockForm {
  background-color: #fff;
  left: 0;
  padding-bottom: 20px;
  padding-top: 20px;
  position: fixed;
  right: 0;
  transition: 0.3s ease 0s;
  top: 115px;
  z-index: 105;
}

.header__blockForm:not(.show) {
  opacity: 0;
  pointer-events: none;
}

.header__blockForm.show {
  top: 102px;
}

.header__blockForm .elementor-search-form__container {
  display: flex;
  height: 42px;
}

.header__blockForm form {
  height: 100%;
}

.header__blockForm form input {
  border: none;
  height: 100%;
  outline: none;
}

.header__blockForm form input[type=search] {
  background-color: #f7f7f7;
  font-size: 15px;
  padding-left: 15px;
  padding-right: 15px;
  width: calc(100% - 112px);
}

.header__blockForm form input[type=search]::-moz-placeholder {
  color: #8E90A0;
}

.header__blockForm form input[type=search]::placeholder {
  color: #8E90A0;
}

.header__blockForm form button[type=submit] {
  align-items: center;
  background-color: #00b6da;
  color: #fff;
  display: flex;
  justify-content: center;
  font-size: 15px;
  min-width: 100px;
  margin-left: 12px;
  padding: 5px 12px 4px;
  text-align: center;
}

.header__blockForm form .fas.fa-search {
  display: none;
}

.header .select,
.language {
  margin-left: 20px;
  position: relative;
}

.header .select.open .select__body,
.header .select.open .language__body,
.language.open .select__body,
.language.open .language__body {
  background-color: #fff;
  pointer-events: all;
  opacity: 1;
  visibility: visible;
}

.header .select__header,
.language__header {
  align-items: center;
  background: rgba(255, 255, 255, 0.11);
  display: flex;
  height: 28px;
  padding: 6px;
  width: 66px;
}

.header .select__icon,
.language__icon {
  align-items: center;
  display: flex;
  justify-content: center;
  height: 16px;
  margin-right: 10px;
  width: 16px;
}

.header .select__icon img,
.header .select__icon svg,
.language__icon img,
.language__icon svg {
  max-height: 100%;
  max-width: 100%;
}

.header .select__current,
.header .select__item,
.language__current,
.language__item {
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
}

.header .select .select__body,
.header .select__body,
.language .select__body,
.language__body {
  opacity: 0;
  padding-bottom: 7px;
  padding-top: 7px;
  pointer-events: none;
  position: absolute;
  visibility: hidden;
  transition: 0.3s ease 0s;
  width: 100%;
}

.header .select__item,
.language__item {
  padding: 8px;
}

.header .select__item.current-lang,
.language__item.current-lang {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.header .select__item.current-lang,
.header .select__item a:hover,
.language__item.current-lang,
.language__item a:hover {
  color: #00b6da;
}

.header .select__item a,
.language__item a {
  display: table;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  text-decoration: none;
  transition: 0.2s ease 0s;
}

.header .select .select-currency__item,
.language .select-currency__item {
  display: table;
  cursor: pointer;
  margin-left: auto;
  margin-right: auto;
  padding-left: 0;
  padding-right: 0;
  text-align: center;
  text-decoration: none;
  transition: 0.2s ease 0s;
}

.header .select .select-currency__item:hover,
.language .select-currency__item:hover {
  color: #00b6da;
}

/* Top menu */

.header .menu {
  width: 100%;
}

.header .menu__icon {
  display: none;
  font-size: 0;
}

.header .menu__list > li {
  position: relative;
}

.header .menu__list > li.menu-item-has-children::after {
  /*background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='12' height='8' viewBox='0 0 12 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M11 1.55566L6 6.44455L1 1.55566' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  content: '';
  display: block;
  height: 8px;
  font-size: 0;
  margin-left: 8px;
  position: relative;
  transition: 0.3s ease 0s;
  width: 12px;
  */
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 3px solid black;
  content: "";
  height: 0;
  position: absolute;
  right: 0;
  transform: translate(100%, -50%);
  top: 50%;
  width: 0;
}

.header .menu li {
  list-style: none;
  position: relative;
}

.header .menu a {
  transition: 0.3s ease 0s;
  text-decoration: none;
}

/* ------------------- */

body._pc .header .menu__list > li.menu-item-big-submenu:hover > a {
  position: relative;
  z-index: 1;
}

body._pc .header .menu__list > li:hover .sub-menu {
  opacity: 1;
  pointer-events: all;
  transform: perspective(400px) rotateX(0deg);
  visibility: visible;
}

body._touch .header .menu__list {
  flex: 1 1 auto;
}

body._pc .header .menu__list > li.menu-item-has-children:hover::after,
body._touch .header .menu__list > li._active.menu-item-has-children::after {
  border-top: 3px solid #00b6da;
  right: -6px;
  top: calc(50% - 2px);
  transform: scale(1, -1);
}

/*
#burger {
   background-color: #afafaf;
   cursor: pointer;
   height: 13.33px;
   position: relative;
   //transition-duration: 0.3s;
   min-width: 18.44px;
   max-width: 18.44px;
   z-index: 6;

   @include media-min-801 {
      display: none;
   }

   span {
      background-color: #002024;
      //border-radius: 20px;
      height: 1px;
      left: 50%;
      position: absolute;
      transition-duration: 0.15s;
      transition-delay: 0.15s;
      transition: 0.1s ease 0s;
      transform: translate(-50%, -50%);
      top: 50%;
      width: 100%;

      &::before {
         background-color: #002024;
         border-radius: inherit;
         left: 0;
         content: "";
         height: 1px;
         position: inherit;
         top: 12px;
         transition-duration: inherit;
         transition: transform .25s, top .25s .25s;
         width: 100%;
      }
      &::after {
         background-color: #002024;
         border-radius: inherit;
         left: 0;
         content: "";
         height: 1px;
         position: inherit;
         top: 12px;
         transition-duration: inherit;
         transition: transform .25s, top .25s .25s;
         width: 100%;
      }
   }
}
#burger span:after {
   left: 0;
   position: absolute;
   top: -15px;
   height: 4px;
   width: 100%;
   background-color: #002024;
   content: "";
   border-radius: 20px;
   transition-duration: .25s;
   transition: transform .25s, top .25s .25s;

   @include media-max-1099 {
      margin-left: -8px;
      min-width: 50px;
      max-width: 50px;
   }
   @include media-max-1024 {
      height: 2px;
      margin-left: -8px;
      min-width: 38px;
      max-width: 38px;
      top: 11px;
   }
   @include media-max-800 {
      margin-left: -5px;
      min-width: 30px;
      max-width: 30px;
      top: 8px;
   }
}

#burger.open {

   span {
      //transition-duration: 0.1s;
      //transition-delay: .25s;
      //background: transparent;
      //left: 22px;
      //top: 18px;
      //transform: rotateZ(-45deg) translate(-50%, -50%);

      &:before {
         //transition: top .25s, transform .25s .25s;
         //top: 0px;
         //transform: rotateZ(92deg);
      }
   }
}
#burger.open span:after {
   transition: top 0.4s, transform .25s .25s;
   top: 0px;
   transform: rotateZ(45deg);
}*/

/* Page - Home */

.first-screen {
  background-color: #C7E6F4;
  padding-bottom: 363px;
  padding-top: 169px;
  position: relative;
}

.first-screen__content {
  max-width: 571px;
  position: relative;
  z-index: 1;
}

.first-screen h1 {
  font-size: 60px;
  font-weight: 500;
  line-height: 113%;
  margin: 0;
}

.first-screen p {
  color: #59656F;
  font-weight: 500;
  margin-top: 28px;
  padding-right: 8px;
}

.first-screen__wrap-button {
  align-items: center;
  display: flex;
  margin-top: 25px;
}

.first-screen__wrap-button a {
  color: #2A2A29;
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  text-decoration: none;
}

.first-screen__wrap-button a:not(:first-child) {
  margin-left: 25px;
}

.first-screen__wrap-button a.button {
  align-items: center;
  background-color: #00B6DA;
  color: #fff;
  height: 40px;
  display: flex;
  justify-content: center;
  font-size: 12px;
  min-width: 132px;
  padding: 5px 12px;
  text-align: center;
}

.first-screen__bg {
  height: 100%;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.block-search-form .tabs {
  margin-top: -105px;
  position: relative;
  z-index: 1;
}

.block-search-form .tabs__wrap-tab {
  display: flex;
}

.block-search-form .tabs__wrap-tab.block-switching .tab {
  pointer-events: none;
}

.block-search-form .tabs__wrap-tab .tab {
  align-items: center;
  background: rgba(255, 255, 255, 0.67);
  font-size: 12px;
  font-weight: 400;
  line-height: 22px;
  height: 36px;
  display: flex;
  margin-bottom: -1px;
  padding: 0 18px 0 45px;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.block-search-form .tabs__wrap-tab .tab::after {
  border-bottom: 36px solid rgba(255, 255, 255, 0.67);
  border-right: 24px solid transparent;
  content: "";
  height: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  transform: translate(100%, -50%);
  top: 50%;
  width: 0;
}

.block-search-form .tabs__wrap-tab .tab:not(:first-child).tab-active {
  padding-left: 18px;
}

.block-search-form .tabs__wrap-tab .tab:not(:first-child).tab-active::before {
  border-left: 24px solid transparent;
  border-top: 36px solid #fff;
  content: "";
  height: 0;
  pointer-events: none;
  position: absolute;
  left: 0;
  transform: translate(-100%, 0);
  top: 0;
  width: 0;
}

.block-search-form .tabs__wrap-tab .tab:first-child,
.block-search-form .tabs__wrap-tab .tab.tab-active {
  padding-left: 25px;
}

.block-search-form .tabs__wrap-tab .tab:first-child:not(.tab-active) {
  padding-right: 45px;
}

.block-search-form .tabs__wrap-tab .tab.tab-active {
  background-color: #fff;
  color: #8E90A0;
  padding-right: 18px;
}

.block-search-form .tabs__wrap-tab .tab.tab-active::after {
  border-bottom: 36px solid #fff;
}

.block-search-form .tabs .tabContent {
  background-color: #fff;
  width: 100%;
}

.block-search-form .tabs .tabContent.content-active {
  padding: 9px 26px 19px;
}

.block-search-form .search-form {
  margin-bottom: 11px;
}

.block-search-form .advanced-search-button {
  color: #00B6DA;
  font-size: 12px;
  font-weight: 400;
  line-height: 22px;
}

.block-search-form .advanced-search-button.expanded .advanced {
  display: none;
}

.block-search-form .advanced-search-button.expanded .simple {
  display: block;
}

.block-search-form .advanced-search-button .simple {
  display: none;
}

.search-form__content {
  align-items: flex-end;
  display: flex;
  margin-left: -8.5px;
  margin-right: -8.5px;
}

.search-form__content.all-fields {
  flex-wrap: wrap;
}

/*.search-form__content.all-fields .search-form__column {
  width: calc(20% - 17px);
}*/

.search-form__content.all-fields .search-form__column.extended-field {
  display: block;
}

.search-form__content.all-fields .search-form__column.id {
  margin-right: 8px !important;
}

.search-form__content.all-fields .search-form__submit {
  margin: 20px 9px 0 auto;
}

.search-form__content.all-fields .wrap-field.price span {
  width: 1px;
}

.search-form__column {
  margin: 20px 8.5px 0;
}

.search-form__column > label {
  color: #71737F;
  display: table;
  font-size: 12px;
  font-weight: 400;
  line-height: 22px;
  margin-bottom: 1px;
}

.search-form__column.type {
  min-width: 150px;
  width: calc(16% - 17px - 14.7142px);
}

.search-form__column.select {
  width: calc(14% - 14px - 14.7142px);
}

.search-form__column.id {
  margin-right: 17px !important;
  width: calc(12% - 25px - 14.7142px);
}

.search-form__column.extended-field {
  display: none;
}

.search-form .wrap-field {
  background-color: #F7F7F7;
  height: 36px;
}

.search-form .wrap-field input,
.search-form .wrap-field select {
  border: none;
  color: #8E90A0;
  height: 100%;
  font-size: 12px;
  font-weight: 400;
  line-height: 22px;
  padding-left: 11px;
  padding-right: 11px;
  outline: none;
  width: 100%;
}

.search-form .wrap-field input {
  background: none;
  /* Chrome, Safari, Edge, Opera */
  /* Firefox */
}

.search-form .wrap-field input::-webkit-outer-spin-button,
.search-form .wrap-field input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.search-form .wrap-field input[type=number] {
  -moz-appearance: textfield;
}

.search-form .wrap-field select {
  background-color: #F7F7F7;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding-right: 28px;
}

.search-form .wrap-field .select2-container--custom-select .select2-search__field,
.search-form .wrap-field .select2-container--custom-select .select2-selection__rendered {
  line-height: 17px !important;
}

.search-form .wrap-field.wrap-radio {
  background: none;
  display: flex;
}

.search-form .wrap-field.wrap-radio label {
  position: relative;
  width: calc(50% - 4.5px);
}

.search-form .wrap-field.wrap-radio label:nth-child(odd) {
  margin-right: 4.5px;
}

.search-form .wrap-field.wrap-radio label:nth-child(even) {
  margin-left: 4.5px;
}

.search-form .wrap-field.wrap-radio input {
  bottom: 0;
  left: 0;
  cursor: pointer;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 0;
}

.search-form .wrap-field.wrap-radio input::after {
  background-color: #F7F7F7;
  bottom: 0;
  content: "";
  cursor: pointer;
  left: 0;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 1;
}

.search-form .wrap-field.wrap-radio input.sale {
  position: relative;
}

.search-form .wrap-field.wrap-radio input.sale::before {
  background: url("../images/svg/radio-sale-default.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  height: 16px;
  left: 7px;
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  width: 16px;
  z-index: 2;
}

.search-form .wrap-field.wrap-radio input.rent {
  position: relative;
}

.search-form .wrap-field.wrap-radio input.rent::before {
  background: url("../images/svg/radio-rent-default.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  height: 16px;
  left: 7px;
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  width: 16px;
  z-index: 2;
}

.search-form .wrap-field.wrap-radio input:checked::after {
  background-color: #00B6DA;
  color: #fff;
}

.search-form .wrap-field.wrap-radio input:checked.sale::before {
  background: url("../images/svg/radio-sale-checked.svg");
}

.search-form .wrap-field.wrap-radio input:checked.rent::before {
  background: url("../images/svg/radio-rent-checked.svg");
}

.search-form .wrap-field.wrap-radio input:checked ~ span {
  color: #fff;
}

.search-form .wrap-field.wrap-radio span {
  align-items: center;
  color: #2d2d2d;
  height: 100%;
  display: flex;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  line-height: 22px;
  left: 10px;
  padding: 5px;
  pointer-events: none;
  position: absolute;
  top: 0;
  text-align: center;
  width: 100%;
  z-index: 1;
}

.search-form .wrap-field.wrap-select {
  position: relative;
}

.search-form .wrap-field.wrap-select::after {
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 4px solid black;
  content: "";
  height: 0;
  pointer-events: none;
  position: absolute;
  right: 17px;
  bottom: 16px;
  width: 0;
}

.search-form .wrap-field.wrap-select .select2-container--custom-select {
  width: 100% !important;
}

.search-form .wrap-field.wrap-select .select2-container--custom-select .selection {
  height: 36px;
  line-height: 36px;
}

.search-form .wrap-field.wrap-select .select2-container--custom-select .select2-search__field,
.search-form .wrap-field.wrap-select .select2-container--custom-select .select2-results__option,
.search-form .wrap-field.wrap-select .select2-container--custom-select .select2-selection__rendered {
  font-size: 12px;
  font-weight: 400;
  line-height: 22px;
}

.search-form .wrap-field.price {
  display: flex;
}

.search-form .wrap-field.price input:nth-child(1), .search-form .wrap-field.price input:nth-child(3) {
  width: 50%;
}

.search-form .wrap-field.price span {
  background-color: #B7B8C0;
  height: 16px;
  display: block;
  left: 1px;
  pointer-events: none;
  position: relative;
  transform: translateY(-50%);
  top: 50%;
  width: 1px;
  z-index: 1;
}

.search-form__submit {
  background-color: #00B6DA;
  color: #fff;
  height: 36px;
  font-size: 12px;
  font-weight: 500;
  line-height: 22px;
  flex: 0 0 103px;
  padding: 5px 10px;
  width: 103px;
}

.most-popular {
  overflow: hidden;
  padding-bottom: 63px;
  padding-top: 99px;
}

.most-popular__head {
  margin-left: auto;
  margin-right: auto;
  max-width: 570px;
}

.most-popular__title,
.most-popular__desc {
  text-align: center;
}

.most-popular__desc {
  margin-top: 19px;
}

.most-popular .object__blockImage {
  padding-bottom: 121.4%;
}

.most-popular .swiper-pagination.square-dots {
  margin-top: 53px;
}

.most-popular__button {
  align-items: center;
  border: 1px solid #2A2A29;
  display: flex;
  justify-content: center;
  height: 48px;
  font-weight: 500;
  margin: 60px auto 0;
  padding: 5px 10px;
  text-align: center;
  text-decoration: none;
  width: 191px;
}

.uptrends-offer {
  background-color: #EAEAEA;
  padding-bottom: 100px;
  padding-top: 98px;
}

.uptrends-offer__item p {
  color: #59656F;
  margin-top: 43px;
  max-width: 485px;
}

.uptrends-offer .testimonials__aboveTitle,
.uptrends-offer .testimonials__title {
  padding-right: 0;
}

.uptrends-offer .testimonials__title {
  max-width: 545px;
}

.uptrends-offer__list li {
  align-items: center;
  display: flex;
  list-style: none;
  font-weight: 500;
  position: relative;
}

.uptrends-offer__list li:not(:last-child) {
  margin-bottom: 20px;
}

.uptrends-offer__list li::before {
  align-items: center;
  background-color: #fff;
  content: "";
  display: flex;
  height: 50px;
  flex: 0 0 50px;
  margin-right: 25px;
  width: 50px;
}

.uptrends-offer__list li.we-sell-real-estate::before {
  background: #fff url("../images/we-sell-real-estate.png");
  background-repeat: no-repeat;
  background-position: center;
}

.uptrends-offer__list li.lowest-price::before {
  background: #fff url("../images/lowest-price.png");
  background-repeat: no-repeat;
  background-position: center;
}

.uptrends-offer__list li.passive-income::before {
  background: #fff url("../images/passive-income.png");
  background-repeat: no-repeat;
  background-position: center;
}

.uptrends-offer__list li.we-will-select-and-arrange::before {
  background: #fff url("../images/we-will-select-and-arrange.png");
  background-repeat: no-repeat;
  background-position: center;
}

.uptrends-offer__list li.only-on-our-website::before {
  background: #fff url("../images/only-on-our-website.png");
  background-repeat: no-repeat;
  background-position: center;
}

.save-up-to {
  padding-bottom: 88px;
  padding-top: 85px;
  position: relative;
}

.save-up-to::before {
  background-color: rgba(240, 82, 34, 0.81);
  bottom: 0;
  content: "";
  left: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 1;
}

.save-up-to .n_container {
  position: relative;
  z-index: 1;
}

.save-up-to__title {
  color: #fff;
  font-size: 40px;
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
  text-align: center;
}

.save-up-to__button {
  align-items: center;
  border: 1px solid #fff;
  color: #fff;
  height: 48px;
  display: flex;
  justify-content: center;
  margin: 35px auto 0;
  width: 208px;
  padding: 5px 10px;
  text-align: center;
  text-transform: uppercase;
  text-decoration: none;
}

.save-up-to__bg {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: 0 60%;
     object-position: 0 60%;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.section-objects {
  overflow: hidden;
  padding-bottom: 11px;
  padding-top: 80px;
}

.section-objects__wrapTitle {
  align-items: flex-end;
  display: flex;
}

.section-objects__left {
  width: calc(100% - 523px);
}

.section-objects__right {
  width: 523px;
}

.section-objects__right p {
  color: #59656F;
  position: relative;
  top: 3px;
}

.section-objects .tabs {
  margin-top: 50px;
}

.section-objects .tabs__scroll {
  position: relative;
}

.section-objects .tabs__scroll::after {
  border-bottom: 2px solid #D4DADF;
  bottom: 0;
  content: "";
  left: 0;
  display: block;
  height: 1px;
  pointer-events: none;
  position: absolute;
  right: 0;
  z-index: 0;
}

.section-objects .tabs__wrap-tab {
  display: flex;
  justify-content: space-between;
  margin-right: -40px;
  padding-bottom: 2px;
  padding-right: 40px;
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
  width: calc(100% + 40px);
}

.section-objects .tabs__wrap-tab::-webkit-scrollbar {
  width: 0 !important;
  height: 0;
  display: none !important;
  background: transparent;
}

.section-objects .tabs__wrap-tab .tab {
  border-bottom: 2px solid transparent;
  color: #B3BBC1;
  font-weight: 500;
  margin-bottom: -2px;
  padding: 12px 39px;
  white-space: nowrap;
}

.section-objects .tabs__wrap-tab .tab.tab-active {
  border-color: #02d5ff;
  color: #00B6DA;
  position: relative;
  z-index: 5;
}

.section-objects .tabContent {
  position: relative;
}

.section-objects .tabContent.content-active {
  padding-bottom: 50px;
  padding-top: 49px;
}

.section-objects .tabContent .swiper-pagination.square-dots {
  bottom: 0;
  left: 0;
  margin: 0;
  position: absolute;
  transform: translateY(158%);
  text-align: left;
  width: calc(100% - 250px);
}

.section-objects .newest-objects__slider {
  margin-top: 0;
}

.section-objects__bottom {
  display: flex;
}

.section-objects__button {
  align-items: center;
  border: 1px solid #2A2A29;
  height: 44px;
  display: inline-flex;
  font-weight: 500;
  margin-left: auto;
  min-width: 120px;
  padding: 5px 39px 5px 20px;
  position: relative;
  text-decoration: none;
}

.section-objects__button::after {
  background: url("../images/svg/arrow-button-black.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  display: block;
  height: 20px;
  position: absolute;
  right: 19px;
  transform: translateY(-50%);
  top: 50%;
  width: 20px;
}

.testimonials {
  overflow: hidden;
  padding-bottom: 80px;
  padding-top: 80px;
}

.testimonials__aboveTitle,
.testimonials__title {
  padding-right: 150px;
}

.testimonials__aboveTitle {
  color: #00B6DA;
  font-size: 16px;
  font-weight: 700;
  line-height: 120%;
  margin-bottom: 12px;
  text-transform: uppercase;
}

.testimonials__title span {
  color: #00B6DA;
}

.testimonials__wrap-slider {
  position: relative;
}

.testimonials__slider {
  margin-top: 60px;
}

.testimonials .swiper-slide {
  height: auto;
}

.testimonials .swiper-slide.swiper-slide-active .testimonials__testimonia {
  border-color: #F05222;
}

.testimonials__testimonia {
  background-color: #fff;
  border: 1px solid #D4DADF;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding: 18px 19px 19px;
  transition: border 0.3s ease 0s;
  width: 100%;
}

.testimonials__testimonia h3 {
  font-size: 18px;
  line-height: 120%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -moz-box;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.testimonials__testimonia p {
  color: #59656F;
  font-size: 12px;
  line-height: 120%;
  margin: 16px 0 0 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -moz-box;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}

.testimonials__testimonia .bottom {
  align-items: center;
  display: flex;
  margin-top: auto;
  padding-top: 18px;
}

.testimonials__testimonia .bottom .photo {
  height: 40px;
  margin-right: 16px;
  position: relative;
  width: 40px;
}

.testimonials__testimonia .bottom .photo img {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.testimonials__testimonia .bottom .info h4,
.testimonials__testimonia .bottom .info p {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -moz-box;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.testimonials__testimonia .bottom .info h4 {
  font-size: 14px;
  line-height: 120%;
  margin: 0;
}

.testimonials__testimonia .bottom .info p {
  font-size: 10px;
  line-height: 120%;
  margin: 3px 0 0 !important;
}

.testimonials__button {
  align-items: center;
  border: 1px solid #2A2A29;
  color: #2A2A29;
  display: inline-flex;
  justify-content: center;
  height: 48px;
  font-weight: 500;
  margin-top: 60px;
  min-width: 191px;
  padding: 5px 10px;
  text-decoration: none;
}

.slider-buttons {
  display: flex;
  justify-content: space-between;
  height: 40px;
  flex: 0 0 97px;
  width: 97px;
}

.slider-buttons .swiper-button-prev,
.slider-buttons .swiper-button-next {
  align-items: center;
  border: 1px solid #D4DADF;
  cursor: pointer;
  display: flex;
  justify-content: center;
  height: 40px;
  opacity: 1;
  margin: 0;
  position: sticky;
  width: 40px;
}

.slider-buttons .swiper-button-prev::after,
.slider-buttons .swiper-button-next::after {
  display: none;
}

.slider-buttons .swiper-button-prev.swiper-button-lock,
.slider-buttons .swiper-button-next.swiper-button-lock {
  display: none;
}

.slider-buttons .swiper-button-prev svg,
.slider-buttons .swiper-button-next svg {
  height: 20px;
  width: 20px;
}

.study-tour {
  padding-bottom: 50px;
  padding-top: 50px;
}

.study-tour__block {
  color: #fff;
  border-radius: 8px;
  padding: 60px 50px;
  position: relative;
}

.study-tour__block::before {
  background: rgba(40, 40, 40, 0.7);
  border-radius: inherit;
  bottom: 0;
  content: "";
  left: 0;
  height: 100%;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}

.study-tour__title,
.study-tour__columns,
.study-tour__button {
  position: relative;
  z-index: 1;
}

.study-tour__columns {
  margin-top: 68px;
  padding-bottom: 50px;
}

.study-tour__column p,
.study-tour__column li {
  font-weight: 500;
}

.study-tour__column:nth-child(2) ul {
  margin-top: 0;
}

.study-tour__column:nth-child(2) ul li {
  margin-bottom: 38px;
}

.study-tour__column ul {
  margin-top: 25px;
}

.study-tour__column ul li {
  list-style: none;
  margin-bottom: 19px;
  padding-left: 24px;
  position: relative;
}

.study-tour__column ul li::before {
  background-color: #fff;
  border-radius: 50px;
  content: "";
  display: block;
  height: 4px;
  left: 10px;
  position: absolute;
  top: 8px;
  width: 4px;
}

.study-tour__button {
  align-items: center;
  border: 1px solid #fff;
  height: 44px;
  display: inline-flex;
  font-weight: 500;
  margin-top: 80px;
  min-width: 178px;
  padding: 5px 39px 5px 24px;
  position: relative;
  text-decoration: none;
}

.study-tour__button::after {
  background: url("../images/svg/arrow-button.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  display: block;
  height: 20px;
  position: absolute;
  right: 11px;
  transform: translateY(-50%);
  top: 50%;
  width: 20px;
}

.study-tour__bg {
  border-radius: inherit;
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.study-tour.team-page {
  padding-bottom: 160px;
  padding-top: 77px;
  text-align: center;
}

.study-tour.team-page .n_container {
  padding-left: 40px;
  padding-right: 40px;
}

.study-tour.team-page .study-tour__block {
  border-radius: 8px;
  padding-bottom: 89px;
}

.study-tour.team-page .study-tour__content {
  margin: 53px auto 0;
  max-width: 794px;
  padding-bottom: 12px;
  position: relative;
  z-index: 1;
}

.study-tour.team-page .study-tour__content p {
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
}

.top10 {
  padding-bottom: 48px;
  padding-top: 45px;
  position: relative;
}

.top10.different-overlay-color::before {
  background-color: rgba(240, 82, 34, 0.81);
}

.top10.different-overlay-color .top10__bg {
  -o-object-position: 0 64%;
     object-position: 0 64%;
}

.top10::before {
  background-color: rgba(255, 172, 19, 0.81);
  bottom: 0;
  content: "";
  left: 0;
  height: 100%;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 0;
}

.top10 .n_container {
  position: relative;
  z-index: 1;
}

.top10__title {
  color: #fff;
  font-size: 40px;
  line-height: 120%;
  text-align: center;
}

.top10__desc {
  color: #fff;
  font-size: 18px;
  line-height: 150%;
  margin-top: 25px;
  text-align: center;
}

.top10__buttons {
  display: flex;
  justify-content: center;
  margin-top: 34px;
}

.top10__button {
  align-items: center;
  border: 1px solid #fff;
  color: #fff;
  display: flex;
  justify-content: center;
  height: 48px;
  font-weight: 500;
  line-height: 22px;
  margin: 0 6.5px;
  min-width: 150px;
  padding: 5px 28px;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
}

.top10__button.color {
  background-color: #fff;
  color: #000;
}

.top10__bg {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1;
}

.newest-objects {
  background-color: #E3F3F1;
  overflow: hidden;
  padding-bottom: 100px;
  padding-top: 96px;
}

.newest-objects__title {
  text-align: center;
}

.newest-objects__desc {
  color: #59656F;
  font-weight: 500;
  margin: 19px auto 0;
  max-width: 554px;
  text-align: center;
}

.newest-objects__slider {
  margin: 60px auto 0;
  max-width: 1320px;
  overflow: hidden;
}

.newest-objects__button {
  align-items: center;
  border: 1px solid #2A2A29;
  color: inherit;
  display: flex;
  justify-content: center;
  font-weight: 500;
  height: 48px;
  margin: 60px auto 0;
  padding: 5px 10px;
  text-transform: uppercase;
  text-decoration: none;
  text-align: center;
  width: 191px;
}

.object__blockImage {
  background-color: #fff;
  padding-bottom: 72.4%;
  position: relative;
}

.object__blockImage .tags {
  left: 11px;
  position: absolute;
  top: 7px;
  z-index: 1;
}

.object__blockImage .tag {
  border-radius: 8px;
  color: #fff;
  font-size: 10px;
  font-weight: 500;
  line-height: normal;
  padding: 2px 8px 4px;
}

.object__blockImage .tag:not(:last-child) {
  margin-right: 3px;
}

.object__blockImage .tag.vivid-orange {
  background-color: #FFAC13;
}

.object__blockImage .tag.vivid-red {
  background-color: #F05222;
}

.object__blockImage img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.object__id {
  background-color: rgba(255, 255, 255, 0.84);
  bottom: 10px;
  font-size: 10px;
  line-height: 100%;
  padding: 4px 8px;
  position: absolute;
  right: 10px;
  z-index: 1;
}

.object__name {
  font-size: 20px;
  font-weight: 500;
  line-height: 120%;
  margin: 13px 0 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -moz-box;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.object__list {
  margin-top: 18px;
}

.object__list li {
  display: flex;
  color: #59656F;
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
  list-style: none;
  margin: 13px 0;
  padding-left: 24px;
  position: relative;
}

.object__list li strong {
  color: #2A2A29;
  font-weight: 500;
  margin-left: auto;
  padding-left: 5px;
}

.object__list li::before {
  content: "";
  height: 16px;
  left: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
}

.object__list li.location-icon::before {
  background: url("../images/svg/location-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.object__list li.square-icon::before {
  background: url("../images/svg/square-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.object__list li.rooms-icon::before {
  background: url("../images/svg/rooms-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.object__list li.to-sea-icon::before {
  background: url("../images/svg/to-sea-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.object__button {
  align-items: center;
  background-color: #00B6DA;
  color: #fff;
  font-size: 18px;
  font-weight: 500;
  line-height: normal;
  display: flex;
  justify-content: center;
  height: 50px;
  margin-top: 19px;
  padding: 5px 12px;
  text-decoration: none;
  text-align: center;
  width: 100%;
}

.object__button .currency {
  font-size: 11px;
  margin-right: 8px;
}

.quantity-with-icon {
  align-items: center;
  bottom: 14px;
  color: #fff;
  font-size: 12px;
  font-weight: 400;
  line-height: 100%;
  display: flex;
  position: absolute;
  right: 15px;
  z-index: 1;
}

.quantity-with-icon a {
  outline: none;
}

.quantity-with-icon a:not(.icon) {
  display: none;
}

.quantity-with-icon img {
  display: block;
  height: 14px;
  margin-left: 6px;
  pointer-events: all;
  position: static;
  width: 14px;
}

.swiper-pagination.square-dots {
  margin-top: 43px;
  position: static;
}

.swiper-pagination.square-dots .swiper-pagination-bullet {
  background-color: #E7E7E7;
  border-radius: 0;
  height: 8px;
  opacity: 1;
  margin-left: 3px;
  margin-right: 3px;
  width: 8px;
}

.swiper-pagination.square-dots .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #00B6DA;
  width: 36px;
}

.section-video {
  background-color: #F4F4F4;
  padding-bottom: 64px;
  padding-top: 76px;
}

.section-video .section-blog__previewPosts {
  margin-top: 46px;
}

.section-video .section-blog__previewPost {
  padding: 15px 15px 23px;
}

.section-video .section-blog__previewPost .previewPost-blockImage {
  height: 116px;
  width: 130px;
}

.section-video .section-blog__previewPost .previewPost-blockImage::before {
  background: rgba(0, 0, 0, 0.18);
  bottom: 0;
  content: "";
  left: 0;
  height: 100%;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 1;
}

.section-video .section-blog__previewPost .previewPost-blockImage::after {
  background: url("../images/svg/section-video-play.svg");
  background-position: center;
  background-repeat: no-repeat;
  content: "";
  left: 50%;
  height: 32px;
  pointer-events: none;
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  width: 32px;
  z-index: 2;
}

.section-video .section-blog__previewPost .previewPost-blockInfo {
  width: calc(100% - 130px);
}

.section-video .section-blog__previewPost .previewPost-blockInfoBottom {
  margin-bottom: -12px;
}

.section-video__button {
  align-items: center;
  border: 1px solid #59656F;
  color: #59656F;
  display: inline-flex;
  font-weight: 500;
  height: 44px;
  margin: 43px 0 0;
  min-width: 151px;
  padding: 5px 13px 5px 41px;
  position: relative;
  text-decoration: none;
}

.section-video__button::after {
  background: url("../images/svg/arrow-button-play.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  left: 14px;
  display: block;
  height: 18px;
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  width: 18px;
}

.section-blog {
  padding-bottom: 80px;
  padding-top: 40px;
}

.section-blog .tabs__wrap-tab .tab {
  color: #DEE5E6;
  font-size: 45px;
  font-weight: 600;
  line-height: 120%;
}

.section-blog .tabs__wrap-tab .tab.tab-active {
  color: #2A2A29;
}

.section-blog .tabs__wrap-tab .tab.tab-active span {
  color: #00B6DA;
}

.section-blog .tabContent.content-active {
  padding-top: 81px;
}

.section-blog__previewPosts {
  display: flex;
  flex-wrap: wrap;
}

.section-blog__previewPost {
  border: 1px solid #D4DADF;
  display: flex;
  flex-wrap: wrap;
  padding: 16px 16px 6px;
}

.section-blog__previewPost .previewPost-blockImage {
  height: 100px;
  position: relative;
  width: 100px;
}

.section-blog__previewPost .previewPost-blockImage img {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.section-blog__previewPost .previewPost-blockInfo {
  display: flex;
  flex-direction: column;
  padding-left: 23px;
  width: calc(100% - 100px);
}

.section-blog__previewPost .previewPost-blockInfo h2,
.section-blog__previewPost .previewPost-blockInfo p {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -moz-box;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.section-blog__previewPost .previewPost-blockInfo h2 {
  font-size: 16px;
  font-weight: 600;
  line-height: 120%;
  margin: -1px 0 0;
}

.section-blog__previewPost .previewPost-blockInfo p {
  color: #59656F;
  font-size: 14px;
  line-height: 120%;
  margin: 11px 0 0 !important;
}

.section-blog__previewPost .previewPost-blockInfoBottom {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 7px;
}

.section-blog__previewPost .previewPost-blockInfoBottom p,
.section-blog__previewPost .previewPost-blockInfoBottom a {
  font-size: 12px;
  line-height: 120%;
}

.section-blog__previewPost .previewPost-blockInfoBottom p {
  color: #59656F;
  margin: 5px 10px 5px 0 !important;
}

.section-blog__previewPost .previewPost-blockInfoBottom a {
  align-items: center;
  color: #00B6DA;
  display: flex;
  margin: 5px 0 5px auto;
  text-decoration: none;
}

.section-blog__previewPost .previewPost-blockInfoBottom a::after {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='21' height='20' viewBox='0 0 21 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M4.8335 10L16.5002 10M16.5002 10L11.5002 5M16.5002 10L11.5002 15' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  content: "";
  display: block;
  height: 20px;
  flex: 0 0 20px;
  margin-left: 9px;
  width: 20px;
}

.section-blog__button {
  align-items: center;
  border: 1px solid #59656F;
  color: #59656F;
  display: inline-flex;
  font-weight: 500;
  height: 44px;
  margin: 42px 0 0;
  min-width: 151px;
  padding: 5px 41px 5px 13px;
  position: relative;
  text-decoration: none;
}

.section-blog__button::after {
  background: url("../images/svg/arrow-button-black.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  display: block;
  height: 20px;
  opacity: 0.5;
  position: absolute;
  right: 13px;
  transform: translateY(-50%);
  top: 50%;
  width: 20px;
}

.contact-us {
  background-color: #fff;
  padding-bottom: 100px;
  padding-top: 100px;
}

.contact-us__desc {
  color: #59656F;
  margin: 27px 0 0 !important;
}

.contact-us__blockImage {
  min-height: 100%;
  padding-bottom: calc(126% + 3px);
  position: relative;
  width: 100%;
}

.contact-us__blockImage img {
  left: 0;
  height: 100%;
  -o-object-position: bottom;
     object-position: bottom;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.contact-us__form {
  margin-top: 30px;
}

.contact-us__form .wrap-fields {
  display: flex;
  flex-wrap: wrap;
}

.contact-us__form .wrap-fields .wrap-field {
  width: 50%;
}

.contact-us__form .wrap-fields .wrap-field:nth-child(odd) {
  padding-right: 12.5px;
}

.contact-us__form .wrap-fields .wrap-field:nth-child(even) {
  padding-left: 12.5px;
}

.contact-us__form .wrap-field {
  margin-top: 21px;
  position: relative;
}

.contact-us__form .wrap-field label {
  display: table;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.contact-us__form .wrap-field input,
.contact-us__form .wrap-field select,
.contact-us__form .wrap-field textarea {
  background-color: #F7F7F7;
  border: none;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  outline: none;
  padding: 5px 10px 7px;
  width: 100%;
}

.contact-us__form .wrap-field input::-moz-placeholder, .contact-us__form .wrap-field select::-moz-placeholder, .contact-us__form .wrap-field textarea::-moz-placeholder {
  color: #8E90A0;
}

.contact-us__form .wrap-field input::placeholder,
.contact-us__form .wrap-field select::placeholder,
.contact-us__form .wrap-field textarea::placeholder {
  color: #8E90A0;
}

.contact-us__form .wrap-field input,
.contact-us__form .wrap-field select {
  height: 45px;
}

.contact-us__form .wrap-field textarea {
  height: 160px;
  padding-top: 6px;
}

.contact-us__form .wrap-field.wrap-select {
  position: relative;
}

.contact-us__form .wrap-field.wrap-select::after {
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 4px solid black;
  content: "";
  height: 0;
  position: absolute;
  right: 17px;
  bottom: 21px;
  width: 0;
}

.contact-us__form .wrap-field select {
  color: #8E90A0;
  position: relative;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.contact-us__form input[type=submit] {
  background-color: #00B6DA;
  border: none;
  color: #fff;
  cursor: pointer;
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  margin-top: 32px;
  padding: 0 10px 2px;
  text-align: center;
  width: 132px;
}

/* Page - Page */

.page__head {
  margin: 0 auto 51px;
  max-width: 575px;
}

.page__title {
  font-size: 45px;
  line-height: 120%;
  font-weight: 600;
  margin: 26px 0 0;
  text-align: center;
}

.page__desc {
  color: #59656F;
  font-weight: 500;
  line-height: 120%;
  margin-top: 19px;
  text-align: center;
}

.apartments {
  margin-top: 31px;
}

.apartments__head {
  align-items: center;
  border-bottom: 2px solid #D4DADF;
  display: flex;
  margin-top: 65px;
  padding-bottom: 19px;
  padding-top: 19px;
}

.apartments__head .left {
  padding-right: 5px;
  width: 45%;
}

.apartments__head .left p {
  font-size: 24px;
  font-weight: 600;
  line-height: 120%;
}

.apartments__head .left p span {
  color: #00B6DA;
  white-space: nowrap;
}

.apartments__head .right {
  padding-left: 5px;
  width: 55%;
}

.apartments__head .right .content {
  align-items: center;
  display: flex;
  justify-content: flex-end;
}

.apartments__head .right .select {
  bottom: 2px;
  min-width: 50px;
  position: relative;
}

.apartments__head .right .select.open .select__body {
  background-color: #fff;
  pointer-events: all;
  opacity: 1;
  visibility: visible;
}

.apartments__head .right .select__header {
  align-items: center;
  background-color: #F4F4F4;
  cursor: pointer;
  display: flex;
  height: 28px;
  padding: 6px;
  width: 100%;
}

.apartments__head .right .select__header p {
  font-size: 12px;
  font-weight: 400;
  line-height: 22px;
  margin: 0 4px 0 0;
}

.apartments__head .right .select__current,
.apartments__head .right .select__item {
  font-size: 12px;
  line-height: 22px;
}

.apartments__head .right .select__current {
  font-weight: 600;
  pointer-events: none;
  white-space: nowrap;
}

.apartments__head .right .select__body {
  background-color: #F4F4F4 !important;
  opacity: 0;
  right: 0;
  min-width: 120px;
  padding: 7px 15px;
  pointer-events: none;
  position: absolute;
  visibility: hidden;
  transition: 0.3s ease 0s;
  width: auto;
  z-index: 5;
}

.apartments__head .right .select__item {
  cursor: pointer;
  display: table;
  font-weight: 500;
  margin: 3px 0 3px auto;
  text-align: right;
  text-decoration: none;
  transition: 0.2s ease 0s;
  white-space: nowrap;
}

.apartments__head .right .select__item:hover {
  color: #00b6da;
}

.apartments__head .right .select__item.current-lang {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.apartments__head .right .select__item.current-lang,
.apartments__head .right .select__item a:hover {
  color: #00b6da;
}

.apartments.grid {
  display: flex;
  flex-wrap: wrap;
  margin-left: -22.5px;
  margin-right: -22.5px;
  padding-bottom: 64px;
}

.apartments.grid .apartment {
  margin: 0 22.5px 50px;
  width: calc(25% - 45px);
}

.apartments.list {
  padding-bottom: 106px;
}

.apartments.list .apartment {
  margin-bottom: 25px;
}

.sort-by-direction {
  bottom: 1px;
  height: 26px;
  margin-right: 1px;
  position: relative;
  width: 26px;
}

.sort-by-direction svg {
  max-height: 100%;
  max-width: 100%;
}

.sort-by-direction.ascending .arrow-up path {
  stroke: #00B6DA;
}

.sort-by-direction.in-descending-order .arrow-down path, .sort-by-direction.descending .arrow-down path {
  stroke: #00B6DA;
}

.type-of-tiles input {
  cursor: pointer;
  height: 27px;
  position: relative;
  width: 27px;
  z-index: 0;
}

.type-of-tiles input:checked {
  cursor: default;
}

.type-of-tiles input:checked:nth-child(1)::after {
  background: #fff url("data:image/svg+xml;charset=UTF-8,%3csvg width='26' height='27' viewBox='0 0 26 27' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M10.0833 8.49561H19.25M10.0833 13.4956H19.25M10.0833 18.4956H19.25' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3ccircle cx='6.75' cy='8.5' r='1' fill='%2300B6DA'/%3e%3ccircle cx='6.75' cy='13.5' r='1' fill='%2300B6DA'/%3e%3ccircle cx='6.75' cy='18.5' r='1' fill='%2300B6DA'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.type-of-tiles input:checked:nth-child(2)::after {
  background: #fff url("data:image/svg+xml;charset=UTF-8,%3csvg width='26' height='27' viewBox='0 0 26 27' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M5 13.5H21M13 5.5V21.5M5 7.5C5 6.96957 5.21071 6.46086 5.58579 6.08579C5.96086 5.71071 6.46957 5.5 7 5.5H19C19.5304 5.5 20.0391 5.71071 20.4142 6.08579C20.7893 6.46086 21 6.96957 21 7.5V19.5C21 20.0304 20.7893 20.5391 20.4142 20.9142C20.0391 21.2893 19.5304 21.5 19 21.5H7C6.46957 21.5 5.96086 21.2893 5.58579 20.9142C5.21071 20.5391 5 20.0304 5 19.5V7.5Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.type-of-tiles input::after {
  bottom: 0;
  content: "";
  left: 0;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 12;
}

.type-of-tiles input:nth-child(1)::after {
  background: #F4F4F4 url("data:image/svg+xml;charset=UTF-8,%3csvg width='26' height='27' viewBox='0 0 26 27' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3c!-- Линии --%3e%3cpath d='M10.0833 8.49561H19.25M10.0833 13.4956H19.25M10.0833 18.4956H19.25' stroke='black' stroke-linecap='round' stroke-linejoin='round'/%3e%3c!-- Точки --%3e%3ccircle cx='6.75' cy='8.5' r='1' fill='black'/%3e%3ccircle cx='6.75' cy='13.5' r='1' fill='black'/%3e%3ccircle cx='6.75' cy='18.5' r='1' fill='black'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.type-of-tiles input:nth-child(2)::after {
  background: #F4F4F4 url("data:image/svg+xml;charset=UTF-8,%3csvg width='26' height='27' viewBox='0 0 26 27' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M5 13.5H21M13 5.5V21.5M5 7.5C5 6.96957 5.21071 6.46086 5.58579 6.08579C5.96086 5.71071 6.46957 5.5 7 5.5H19C19.5304 5.5 20.0391 5.71071 20.4142 6.08579C20.7893 6.46086 21 6.96957 21 7.5V19.5C21 20.0304 20.7893 20.5391 20.4142 20.9142C20.0391 21.2893 19.5304 21.5 19 21.5H7C6.46957 21.5 5.96086 21.2893 5.58579 20.9142C5.21071 20.5391 5 20.0304 5 19.5V7.5Z' stroke='black' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* Page - Apartment */

.apartament__page-head .right {
  text-align: right;
}

.apartament__page-head h1 {
  font-size: 42px;
  font-weight: 600;
  line-height: 120%;
  margin: 0;
}

.apartament__page-head ul {
  display: flex;
  flex-wrap: wrap;
  margin-top: 14px;
}

.apartament__page-head ul li {
  background-color: #fff;
  list-style: none;
}

.apartament__page-head ul li:not(:last-child) {
  margin-right: 4px;
}

.apartament__page-head ul li.favorite {
  align-items: center;
  display: flex;
  padding-left: 4px;
}

.apartament__page-head ul li.favorite.in-favorites::before {
  background: url("../images/svg/apartament-in-favorites-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__page-head ul li.favorite::before {
  background: url("../images/svg/apartament-favorite-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  content: "";
  display: block;
  height: 14px;
  pointer-events: none;
  width: 14px;
}

.apartament__page-head ul li.share {
  align-items: center;
  display: flex;
  padding-left: 4px;
}

.apartament__page-head ul li.share::before {
  background: url("../images/svg/apartament-share-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  content: "";
  display: block;
  height: 15px;
  pointer-events: none;
  width: 15px;
}

.apartament__page-head ul li.print {
  align-items: center;
  display: flex;
  padding-left: 4px;
}

.apartament__page-head ul li.print::before {
  background: url("../images/svg/apartament-print-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  content: "";
  display: block;
  height: 14px;
  pointer-events: none;
  width: 14px;
}

.apartament__page-head ul li a {
  align-items: center;
  color: #8E90A0;
  display: flex;
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  height: 24px;
  padding: 4px 6px;
  text-decoration: none;
}

.apartament__page-head .price {
  align-items: center;
  color: #00B6DA;
  display: flex;
  justify-content: flex-end;
  font-size: 32px;
  font-weight: 600;
  line-height: 120%;
  margin: 0 !important;
}

.apartament__page-head .price span {
  font-size: 15px;
  font-weight: 400;
  margin-right: 8px;
  position: relative;
  top: 1px;
}

.apartament__page-head .id {
  align-items: center;
  display: inline-flex;
  color: #8E90A0;
  font-size: 24px;
  font-weight: 500;
  line-height: 120%;
  margin: 13px 0 0 !important;
}

.apartament__page-head .id::before {
  background: url("../images/svg/id-icon.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  display: block;
  height: 25px;
  margin-right: 7px;
  width: 25px;
}

.apartament__wrap-slider {
  margin-top: 16px;
}

.apartament__wrap-slider .swiper-slide {
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.apartament__wrap-slider .swiper-slide img {
  border-radius: inherit;
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.apartament__big-slider {
  height: 100%;
}

.apartament__big-slider .tags {
  align-items: center;
  left: 14px;
  display: flex;
  position: absolute;
  top: 15px;
  z-index: 2;
}

.apartament__big-slider .tags .tag {
  border-radius: 11.56px;
  color: #fff;
  font-size: 14.44px;
  font-weight: 500;
  line-height: 100%;
  padding: 5px 11px 6px;
}

.apartament__big-slider .tags .tag:not(:last-child) {
  margin-right: 8px;
}

.apartament__big-slider .tags .tag.vivid-orange {
  background-color: #FFAC13;
}

.apartament__big-slider .tags .tag.vivid-red {
  background-color: #F05222;
}

.apartament__gallery-slider {
  height: 100%;
}

.apartament__gallery-slider .swiper-slide {
  cursor: pointer;
  height: auto;
}

.apartament__items {
  padding-bottom: 40px;
  padding-top: 50px;
}

.apartament__itemBlock:not(:first-child) {
  margin-top: 28px;
}

.apartament__itemBlock:not(:first-child) h2 {
  padding-top: 20px;
}

.apartament__itemBlock h2 {
  border-bottom: 2px solid #E1E4E6;
  font-size: 24px;
  font-weight: 600;
  line-height: 120%;
  padding-bottom: 20px;
}

.apartament__itemBlock .text {
  padding-top: 30px;
}

.apartament__itemBlock p {
  font-weight: 500;
  line-height: 26px;
}

.apartament__itemBlock p:not(:last-child) {
  margin-bottom: 26px;
}

.apartament__sidebar {
  height: 100%;
  position: relative;
  width: 100%;
}

.apartament__socialList,
.apartament__shortInfoList {
  padding-top: 30px;
}

.apartament__socialList li,
.apartament__shortInfoList li {
  font-size: 16px;
  line-height: 120%;
  list-style: none;
  position: relative;
}

.apartament__socialList li:not(:first-child),
.apartament__shortInfoList li:not(:first-child) {
  margin-top: 20px;
}

.apartament__shortInfoList li {
  display: flex;
  color: #59656F;
  font-weight: 400;
  padding-left: 28px;
}

.apartament__shortInfoList li::before {
  content: "";
  height: 20px;
  display: block;
  left: 0;
  position: absolute;
  top: 0;
  width: 20px;
}

.apartament__shortInfoList li.id::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M3.33325 14.1663V5.83301L9.16659 14.1663V5.83301M12.4999 14.1663H16.6666M12.4999 8.33301C12.4999 8.99605 12.7194 9.63193 13.1101 10.1008C13.5008 10.5696 14.0307 10.833 14.5833 10.833C15.1358 10.833 15.6657 10.5696 16.0564 10.1008C16.4471 9.63193 16.6666 8.99605 16.6666 8.33301C16.6666 7.66997 16.4471 7.03408 16.0564 6.56524C15.6657 6.0964 15.1358 5.83301 14.5833 5.83301C14.0307 5.83301 13.5008 6.0964 13.1101 6.56524C12.7194 7.03408 12.4999 7.66997 12.4999 8.33301Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li.location::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M7.5 9.16602C7.5 9.82906 7.76339 10.4649 8.23223 10.9338C8.70107 11.4026 9.33696 11.666 10 11.666C10.663 11.666 11.2989 11.4026 11.7678 10.9338C12.2366 10.4649 12.5 9.82906 12.5 9.16602C12.5 8.50297 12.2366 7.86709 11.7678 7.39825C11.2989 6.92941 10.663 6.66602 10 6.66602C9.33696 6.66602 8.70107 6.92941 8.23223 7.39825C7.76339 7.86709 7.5 8.50297 7.5 9.16602Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M14.7141 13.8806L11.1783 17.4164C10.8657 17.7286 10.4421 17.904 10.0003 17.904C9.5586 17.904 9.13493 17.7286 8.82242 17.4164L5.28576 13.8806C4.35344 12.9482 3.71853 11.7603 3.46133 10.4671C3.20412 9.17394 3.33616 7.83352 3.84076 6.61536C4.34535 5.39721 5.19984 4.35604 6.29616 3.62351C7.39248 2.89098 8.68139 2.5 9.99992 2.5C11.3184 2.5 12.6074 2.89098 13.7037 3.62351C14.8 4.35604 15.6545 5.39721 16.1591 6.61536C16.6637 7.83352 16.7957 9.17394 16.5385 10.4671C16.2813 11.7603 15.6464 12.9482 14.7141 13.8806Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li.property::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M4.16667 10H2.5L10 2.5L17.5 10H15.8333M4.16667 10V15.8333C4.16667 16.2754 4.34226 16.6993 4.65482 17.0118C4.96738 17.3244 5.39131 17.5 5.83333 17.5H14.1667C14.6087 17.5 15.0326 17.3244 15.3452 17.0118C15.6577 16.6993 15.8333 16.2754 15.8333 15.8333V10' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M8.33325 10H11.6666V13.3333H8.33325V10Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li.to-sea::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M14.6274 13.9582C13.4553 12.7862 11.8657 12.1279 10.2082 12.1279C8.55075 12.1279 6.96114 12.7862 5.78906 13.9582M14.9999 3.16986C13.8515 2.50685 12.4868 2.32718 11.2059 2.67037C9.92504 3.01356 8.83296 3.8515 8.1699 4.99986L16.8299 9.99986C17.4929 8.85147 17.6726 7.48673 17.3294 6.20587C16.9862 4.925 16.1483 3.83292 14.9999 3.16986Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M13.9433 8.3334C15.325 5.94173 15.7975 3.63006 15 3.17006C14.2025 2.71006 12.4375 4.27507 11.0567 6.66673M12.5 7.50007L10 11.8301M2.5 16.0417C2.75941 15.9128 3.04375 15.8417 3.33333 15.8334C3.65791 15.8266 3.97927 15.8989 4.26965 16.0441C4.56002 16.1893 4.81069 16.403 5 16.6667C5.18931 16.9305 5.43998 17.1442 5.73035 17.2894C6.02073 17.4345 6.34209 17.5069 6.66667 17.5001C6.99125 17.5069 7.3126 17.4345 7.60298 17.2894C7.89336 17.1442 8.14402 16.9305 8.33333 16.6667C8.52264 16.403 8.77331 16.1893 9.06369 16.0441C9.35406 15.8989 9.67542 15.8266 10 15.8334C10.3246 15.8266 10.6459 15.8989 10.9363 16.0441C11.2267 16.1893 11.4774 16.403 11.6667 16.6667C11.856 16.9305 12.1066 17.1442 12.397 17.2894C12.6874 17.4345 13.0088 17.5069 13.3333 17.5001C13.6579 17.5069 13.9793 17.4345 14.2696 17.2894C14.56 17.1442 14.8107 16.9305 15 16.6667C15.1893 16.403 15.44 16.1893 15.7304 16.0441C16.0207 15.8989 16.3421 15.8266 16.6667 15.8334C16.9563 15.8417 17.2406 15.9128 17.5 16.0417' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li.construction::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M3.33341 15.8333C2.89139 15.8333 2.46746 15.6577 2.1549 15.3452C1.84234 15.0326 1.66675 14.6087 1.66675 14.1667C1.66675 13.7246 1.84234 13.3007 2.1549 12.9882C2.46746 12.6756 2.89139 12.5 3.33341 12.5M3.33341 15.8333C3.77544 15.8333 4.19937 15.6577 4.51193 15.3452C4.82449 15.0326 5.00008 14.6087 5.00008 14.1667C5.00008 13.7246 4.82449 13.3007 4.51193 12.9882C4.19937 12.6756 3.77544 12.5 3.33341 12.5M3.33341 15.8333H10.8334M3.33341 12.5H10.8334M10.8334 15.8333C10.3914 15.8333 9.96746 15.6577 9.6549 15.3452C9.34234 15.0326 9.16675 14.6087 9.16675 14.1667C9.16675 13.7246 9.34234 13.3007 9.6549 12.9882C9.96746 12.6756 10.3914 12.5 10.8334 12.5M10.8334 15.8333C11.2754 15.8333 11.6994 15.6577 12.0119 15.3452C12.3245 15.0326 12.5001 14.6087 12.5001 14.1667C12.5001 13.7246 12.3245 13.3007 12.0119 12.9882C11.6994 12.6756 11.2754 12.5 10.8334 12.5' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M6.66675 10.0007V5.83398H8.33341C8.99646 5.83398 9.63234 6.09738 10.1012 6.56622C10.57 7.03506 10.8334 7.67094 10.8334 8.33398V12.5007' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M4.16675 12.5003V10.8337C4.16675 10.6126 4.25455 10.4007 4.41083 10.2444C4.56711 10.0881 4.77907 10.0003 5.00008 10.0003H10.8334M17.6001 8.23366L15.0001 4.16699L10.8334 8.33366M17.6001 8.23366C17.9493 8.58328 18.187 9.02854 18.2833 9.5132C18.3796 9.99787 18.3301 10.5002 18.141 10.9567C17.952 11.4133 17.6319 11.8036 17.2212 12.0783C16.8105 12.353 16.3275 12.4999 15.8334 12.5003C15.1706 12.4997 14.5351 12.236 14.0667 11.767L17.6001 8.23366Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li.layout::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M10.0002 3.33398V16.6673M3.3335 12.5007H10.0002M10.0002 10.0007H16.6668M13.3335 10.0007V16.6673M13.3335 13.334H16.6668M3.3335 5.00065C3.3335 4.55862 3.50909 4.1347 3.82165 3.82214C4.13421 3.50958 4.55814 3.33398 5.00016 3.33398H15.0002C15.4422 3.33398 15.8661 3.50958 16.1787 3.82214C16.4912 4.1347 16.6668 4.55862 16.6668 5.00065V15.0006C16.6668 15.4427 16.4912 15.8666 16.1787 16.1792C15.8661 16.4917 15.4422 16.6673 15.0002 16.6673H5.00016C4.55814 16.6673 4.13421 16.4917 3.82165 16.1792C3.50909 15.8666 3.3335 15.4427 3.3335 15.0006V5.00065Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li.square::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M14.1668 4.16602H15.8335C16.0545 4.16602 16.2665 4.25381 16.4228 4.41009C16.579 4.56637 16.6668 4.77834 16.6668 4.99935V5.83268C16.6668 6.0537 16.579 6.26566 16.4228 6.42194C16.2665 6.57822 16.0545 6.66602 15.8335 6.66602H15.0002C14.7791 6.66602 14.5672 6.75381 14.4109 6.91009C14.2546 7.06637 14.1668 7.27833 14.1668 7.49935V8.33268C14.1668 8.5537 14.2546 8.76566 14.4109 8.92194C14.5672 9.07822 14.7791 9.16602 15.0002 9.16602H16.6668M3.3335 9.99935V14.9993M3.3335 11.666C3.3335 11.224 3.50909 10.8001 3.82165 10.4875C4.13421 10.1749 4.55814 9.99935 5.00016 9.99935H5.41683C5.96936 9.99935 6.49927 10.2188 6.88997 10.6095C7.28067 11.0002 7.50016 11.5301 7.50016 12.0827M7.50016 12.0827V14.9993M7.50016 12.0827V12.916M7.50016 12.0827C7.50016 11.5301 7.71966 11.0002 8.11036 10.6095C8.50106 10.2188 9.03096 9.99935 9.5835 9.99935C10.136 9.99935 10.6659 10.2188 11.0566 10.6095C11.4473 11.0002 11.6668 11.5301 11.6668 12.0827V14.9993' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li.furniture::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M4.16675 8.33333V5C4.16675 4.33696 4.43014 3.70107 4.89898 3.23223C5.36782 2.76339 6.00371 2.5 6.66675 2.5H13.3334C13.9965 2.5 14.6323 2.76339 15.1012 3.23223C15.57 3.70107 15.8334 4.33696 15.8334 5V8.33333' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M13.3334 12.4997V10.833C13.3334 10.3386 13.48 9.85521 13.7547 9.44408C14.0294 9.03296 14.4199 8.71253 14.8767 8.52331C15.3335 8.33409 15.8362 8.28458 16.3211 8.38105C16.8061 8.47751 17.2516 8.71561 17.6012 9.06524C17.9508 9.41487 18.1889 9.86033 18.2854 10.3453C18.3818 10.8302 18.3323 11.3329 18.1431 11.7897C17.9539 12.2465 17.6335 12.637 17.2223 12.9117C16.8112 13.1864 16.3279 13.333 15.8334 13.333V15.833H4.16675V13.333C3.6723 13.333 3.18895 13.1864 2.77782 12.9117C2.3667 12.637 2.04627 12.2465 1.85705 11.7897C1.66783 11.3329 1.61832 10.8302 1.71479 10.3453C1.81125 9.86033 2.04935 9.41487 2.39898 9.06524C2.74861 8.71561 3.19407 8.47751 3.67902 8.38105C4.16398 8.28458 4.66664 8.33409 5.12346 8.52331C5.58027 8.71253 5.97072 9.03296 6.24542 9.44408C6.52013 9.85521 6.66675 10.3386 6.66675 10.833V12.4997M6.66675 9.99968H13.3334M5.83342 15.833V17.4997M14.1667 15.833V17.4997' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li.floor::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M3.33325 7.49967V7.50801M3.33325 3.33301V3.34134M7.49992 3.33301V3.34134M12.4999 3.33301V3.34134M16.6666 3.33301V3.34134M16.6666 7.49967V7.50801M3.33325 11.6663H16.6666V15.833C16.6666 16.054 16.5788 16.266 16.4225 16.4223C16.2662 16.5785 16.0543 16.6663 15.8333 16.6663H4.16659C3.94557 16.6663 3.73361 16.5785 3.57733 16.4223C3.42105 16.266 3.33325 16.054 3.33325 15.833V11.6663Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li.offer-from::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M11.6667 2.5V5.83333C11.6667 6.05435 11.7545 6.26631 11.9108 6.42259C12.0671 6.57887 12.2791 6.66667 12.5001 6.66667H15.8334' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M14.1667 17.5H5.83341C5.39139 17.5 4.96746 17.3244 4.6549 17.0118C4.34234 16.6993 4.16675 16.2754 4.16675 15.8333V4.16667C4.16675 3.72464 4.34234 3.30072 4.6549 2.98816C4.96746 2.67559 5.39139 2.5 5.83341 2.5H11.6667L15.8334 6.66667V15.8333C15.8334 16.2754 15.6578 16.6993 15.3453 17.0118C15.0327 17.3244 14.6088 17.5 14.1667 17.5Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.apartament__shortInfoList li span {
  display: inline-block;
  min-width: 113px;
  padding-right: 8px;
}

.apartament__shortInfoList li strong {
  color: #2A2A29;
  font-weight: 500;
  min-width: 50%;
}

.apartament__socialList li {
  font-weight: 500;
  padding-left: 39px;
}

.apartament__socialList li::before {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M2.5 10C2.5 10.9849 2.69399 11.9602 3.0709 12.8701C3.44781 13.7801 4.00026 14.6069 4.6967 15.3033C5.39314 15.9997 6.21993 16.5522 7.12987 16.9291C8.03982 17.306 9.01509 17.5 10 17.5C10.9849 17.5 11.9602 17.306 12.8701 16.9291C13.7801 16.5522 14.6069 15.9997 15.3033 15.3033C15.9997 14.6069 16.5522 13.7801 16.9291 12.8701C17.306 11.9602 17.5 10.9849 17.5 10C17.5 9.01509 17.306 8.03982 16.9291 7.12987C16.5522 6.21993 15.9997 5.39314 15.3033 4.6967C14.6069 4.00026 13.7801 3.44781 12.8701 3.0709C11.9602 2.69399 10.9849 2.5 10 2.5C9.01509 2.5 8.03982 2.69399 7.12987 3.0709C6.21993 3.44781 5.39314 4.00026 4.6967 4.6967C4.00026 5.39314 3.44781 6.21993 3.0709 7.12987C2.69399 8.03982 2.5 9.01509 2.5 10Z' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M7.5 9.99967L9.16667 11.6663L12.5 8.33301' stroke='%2300B6DA' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  content: "";
  height: 19px;
  display: block;
  left: 0;
  position: absolute;
  top: 1px;
  width: 19px;
}

.apartament__widget h3 {
  font-size: 24px;
  font-weight: 600;
  line-height: 120%;
  margin: 0;
}

.apartament__widget h3 span {
  color: #00B6DA;
}

.apartament__widget p {
  color: #59656F;
  font-size: 14px;
  line-height: 20px;
  margin: 14px 0 0 !important;
}

.apartament__button {
  align-items: center;
  border: 1px solid #8E90A0;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  height: 40px;
  display: flex;
  justify-content: center;
  padding: 5px 10px;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  width: 100%;
}

.apartament__button:not(:last-child) {
  margin-bottom: 8px;
}

.apartament__button.color {
  background-color: #00B6DA;
  border-color: #00B6DA;
  color: #fff;
  margin-top: 29px;
}

.apartament__button.video span,
.apartament__button.calendar-clock span {
  align-items: center;
  display: flex;
}

.apartament__button.video span::before,
.apartament__button.calendar-clock span::before {
  bottom: 1px;
  position: relative;
}

.apartament__button.video span::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.5 8.33333L16.2942 6.43667C16.4212 6.3732 16.5623 6.34323 16.7042 6.34962C16.846 6.35601 16.9839 6.39854 17.1047 6.47317C17.2255 6.5478 17.3252 6.65206 17.3944 6.77606C17.4636 6.90006 17.4999 7.03967 17.5 7.18167V12.8183C17.4999 12.9603 17.4636 13.0999 17.3944 13.2239C17.3252 13.3479 17.2255 13.4522 17.1047 13.5268C16.9839 13.6015 16.846 13.644 16.7042 13.6504C16.5623 13.6568 16.4212 13.6268 16.2942 13.5633L12.5 11.6667V8.33333ZM2.5 6.66667C2.5 6.22464 2.67559 5.80072 2.98816 5.48816C3.30072 5.17559 3.72464 5 4.16667 5H10.8333C11.2754 5 11.6993 5.17559 12.0118 5.48816C12.3244 5.80072 12.5 6.22464 12.5 6.66667V13.3333C12.5 13.7754 12.3244 14.1993 12.0118 14.5118C11.6993 14.8244 11.2754 15 10.8333 15H4.16667C3.72464 15 3.30072 14.8244 2.98816 14.5118C2.67559 14.1993 2.5 13.7754 2.5 13.3333V6.66667Z' stroke='%232A2A29' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  display: inline-block;
  height: 20px;
  margin-right: 9px;
  width: 20px;
}

.apartament__button.calendar-clock span::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='21' height='20' viewBox='0 0 21 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M9.24992 17.5H5.49992C5.05789 17.5 4.63397 17.3244 4.32141 17.0118C4.00885 16.6993 3.83325 16.2754 3.83325 15.8333V5.83333C3.83325 5.39131 4.00885 4.96738 4.32141 4.65482C4.63397 4.34226 5.05789 4.16667 5.49992 4.16667H15.4999C15.9419 4.16667 16.3659 4.34226 16.6784 4.65482C16.991 4.96738 17.1666 5.39131 17.1666 5.83333V8.33333M13.8333 2.5V5.83333M7.16659 2.5V5.83333M3.83325 9.16667H12.1666' stroke='%232A2A29' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M12.1665 14.9998C12.1665 15.8839 12.5177 16.7317 13.1428 17.3569C13.7679 17.982 14.6158 18.3332 15.4998 18.3332C16.3839 18.3332 17.2317 17.982 17.8569 17.3569C18.482 16.7317 18.8332 15.8839 18.8332 14.9998C18.8332 14.1158 18.482 13.2679 17.8569 12.6428C17.2317 12.0177 16.3839 11.6665 15.4998 11.6665C14.6158 11.6665 13.7679 12.0177 13.1428 12.6428C12.5177 13.2679 12.1665 14.1158 12.1665 14.9998Z' stroke='%232A2A29' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M15.5 13.75V15L15.9167 15.4167' stroke='%232A2A29' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  display: inline-block;
  height: 20px;
  margin-right: 9px;
  width: 20px;
}

.relevant-objects {
  margin-bottom: -14px;
  padding-top: 99px;
}

.relevant-objects h2 {
  text-align: center;
}

.relevant-objects__desc {
  color: #59656F;
  font-weight: 500;
  line-height: 120%;
  margin: 19px auto 0;
  max-width: 560px;
  text-align: center;
}

.relevant-objects .apartments {
  margin-top: 61px;
}

.managers-wrap-slider {
  overflow: hidden;
  margin-top: 26px;
}

.managers-wrap-slider .slider-buttons {
  height: 26px;
  position: static;
  transform: translate(0, 0);
  margin: 21px auto 0;
  width: 60px;
}

.managers-wrap-slider .slider-buttons .swiper-button-next,
.managers-wrap-slider .slider-buttons .swiper-button-prev {
  height: 26px;
  width: 26px;
}

.managers-slider h4,
.managers-slider p,
.managers-slider a {
  line-height: 120%;
  text-align: center;
}

.managers-slider h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 18px 0 0;
}

.managers-slider p,
.managers-slider a {
  color: #59656F;
  font-size: 14px;
  font-weight: 400;
}

.managers-slider p {
  margin-bottom: 16px !important;
}

.managers-slider a {
  display: table;
  margin: 6px auto 0;
  text-decoration: none;
}

.managers-slider__image {
  background-color: #fff;
  height: 200px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  width: 286px;
}

.managers-slider__image img {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;
}

.managers-slider__icons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 6px;
}

.managers-slider__icons li {
  height: 17px;
  list-style: none;
  margin: 5px;
  width: 17px;
}

.managers-slider__icons li a {
  height: 100%;
  width: 100%;
}

.managers-slider__icons li a svg,
.managers-slider__icons li a img {
  height: 100%;
  width: 100%;
}

.fancybox__container {
  z-index: 9999;
}

.apartament-big-slider-show-all-photos {
  background-color: #fff;
  border-radius: 6px;
  bottom: 18px;
  left: 14px;
  font-size: 10px;
  font-weight: 500;
  line-height: 100%;
  padding: 7px 8px 7px 26px;
  position: absolute;
  text-decoration: none;
  z-index: 2;
}

.apartament-big-slider-show-all-photos span {
  position: relative;
}

.apartament-big-slider-show-all-photos span::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M7.5 4H7.505M1.5 3C1.5 2.60218 1.65804 2.22064 1.93934 1.93934C2.22064 1.65804 2.60218 1.5 3 1.5H9C9.39782 1.5 9.77936 1.65804 10.0607 1.93934C10.342 2.22064 10.5 2.60218 10.5 3V9C10.5 9.39782 10.342 9.77936 10.0607 10.0607C9.77936 10.342 9.39782 10.5 9 10.5H3C2.60218 10.5 2.22064 10.342 1.93934 10.0607C1.65804 9.77936 1.5 9.39782 1.5 9V3Z' stroke='%232A2A29' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M1.5 7.99991L4 5.49991C4.464 5.05341 5.036 5.05341 5.5 5.49991L8 7.99991' stroke='%232A2A29' stroke-linecap='round' stroke-linejoin='round'/%3e%3cpath d='M7 6.99991L7.5 6.49991C7.964 6.05341 8.536 6.05341 9 6.49991L10.5 7.99991' stroke='%232A2A29' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  content: "";
  display: inline-block;
  height: 12px;
  left: -6px;
  position: absolute;
  transform: translateX(-100%);
  width: 12px;
}

.apartament-wrap-buttons-swiper {
  align-items: center;
  bottom: 18px;
  height: 24px;
  display: flex;
  justify-content: space-between;
  position: absolute;
  right: 18px;
  width: 51px;
  z-index: 2;
}

.apartament-button-swiper {
  background-color: #fff;
  border-radius: 6px;
  height: 100%;
  margin: 0;
  position: sticky;
  width: 24px;
}

.apartament-button-swiper::after {
  display: none;
}

.apartament-button-swiper svg {
  left: 50%;
  pointer-events: none;
  position: absolute;
  top: 50%;
  max-width: 16px;
  transform: translate(-50%, -50%);
}

/* Page - Team */

.team {
  margin-top: 95px;
}

.team-page__head .page__title,
.team-page__head .page__desc {
  text-align: left;
}

.team-page__head .page__title {
  margin-top: -8px;
}

.team-page__head .page__desc {
  max-width: 483px;
  text-align: left;
}

.team__item {
  margin-bottom: 45px;
}

.employee {
  min-height: 100%;
  width: 100%;
}

.employee__blockPhoto {
  padding-bottom: 104%;
  position: relative;
  width: 100%;
}

.employee__blockPhoto img {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.employee__name {
  font-size: 20px;
  font-weight: 600;
  line-height: 150%;
  margin: 22px 0 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -moz-box;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.employee__position {
  color: #59656F;
  font-size: 18px;
  font-weight: 500;
  line-height: 150%;
  margin: 0 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -moz-box;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.employee__text {
  color: #59656F;
  line-height: 120%;
  margin: 16px 0 0 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -moz-box;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.employee__phone {
  display: table;
  line-height: 120%;
  margin-top: 15px;
  text-decoration: none;
}

.employee__social {
  display: flex;
  flex-wrap: wrap;
  margin: 20px -7px 0;
}

.employee__social li {
  height: 24px;
  margin: 5px;
  list-style: none;
  width: 24px;
}

.employee__social li a {
  display: block;
  height: 100%;
  width: 100%;
}

.employee__social li.fb-icon a {
  background: url("../images/svg/employee-fb-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.employee__social li.instagram-icon a {
  background: url("../images/svg/employee-instagram-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.employee__social li.ws-icon a {
  background: url("../images/svg/employee-ws-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.employee__social li.vb-icon a {
  background: url("../images/svg/employee-vb-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* Page - About */

.about-page {
  margin-top: 9px;
}

.about-company__items {
  padding-bottom: 71px;
  padding-top: 71px;
}

.about-company__item h1 {
  margin: 0 0 26px;
}

.about-company__item p {
  color: #000;
  font-size: 18px;
  font-weight: 400;
  line-height: 150%;
}

.about-company__pageImage {
  height: 100%;
  padding-bottom: calc(81% - 2px);
  position: relative;
  width: 100%;
}

.about-company__pageImage img {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.about-company__counters {
  display: flex;
  flex-wrap: wrap;
}

.about-company__counter {
  margin-bottom: 21px;
  padding-left: 50px;
  padding-right: 50px;
  width: 50%;
}

.about-company__counter:nth-last-child(1),
.about-company__counter:nth-last-child(2) {
  margin-bottom: 0;
}

.about-company__counter p {
  color: #000;
  font-size: 16px;
  margin: 0 !important;
}

.about-company__counter p:not(.about-company__number) {
  margin-top: 11px !important;
}

.about-company__counter p.about-company__number {
  font-size: 48px;
  font-weight: 600;
  line-height: 120%;
}

.bg-white {
  background-color: #fff;
  padding-bottom: 9px;
}

.gallery {
  overflow: hidden;
  padding-bottom: 80px;
  padding-top: 67px;
}

.gallery__wrap-slider {
  margin-top: 42px;
}

.gallery__wrap-slider .swiper-slide {
  display: block;
  padding-bottom: calc(49% - 3px);
  position: relative;
}

.gallery__wrap-slider .swiper-slide img {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.gallery__navigation {
  align-items: center;
  display: flex;
  margin-top: 88px;
  position: relative;
}

.gallery__navigation .swiper-pagination,
.gallery__navigation .slider-buttons {
  position: static;
}

.gallery__navigation .swiper-pagination {
  padding: 4px 15px 0 0;
  text-align: left;
}

.gallery__navigation .swiper-pagination .swiper-pagination-bullet {
  background-color: #B3BBC1;
  height: 8px;
  margin: 4px;
  opacity: 1;
  width: 8px;
}

.gallery__navigation .swiper-pagination .swiper-pagination-bullet:first-child {
  margin-left: 0;
}

.gallery__navigation .swiper-pagination .swiper-pagination-bullet-active {
  background-color: #000;
}

.gallery__navigation .slider-buttons {
  margin: 0;
  transform: translate(0);
}

.our-customers {
  background-color: #fff;
  padding-bottom: 112px;
  padding-top: 112px;
}

.our-customers__item h2 {
  margin-bottom: 43px;
}

.our-customers__blockImage {
  position: relative;
  width: 100%;
}

.our-customers__blockImage img {
  left: 0;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.our-customers__list li {
  color: #000;
  list-style: none;
  font-size: 16px;
  font-weight: 400;
  line-height: 150%;
  margin: 32px 0;
  padding-left: 64px;
  position: relative;
}

.our-customers__list li::before {
  content: "";
  height: 48px;
  left: 0;
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  width: 48px;
}

.our-customers__list li.home-icon::before {
  background: url("../images/svg/our-customers-home-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.our-customers__list li.money-icon::before {
  background: url("../images/svg/our-customers-money-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.our-customers__list li.globe-icon::before {
  background: url("../images/svg/our-customers-globe-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.our-customers__list li.coins-icon::before {
  background: url("../images/svg/our-customers-coins-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.our-customers__list li.palm-icon::before {
  background: url("../images/svg/our-customers-palm-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.our-customers__button {
  align-items: center;
  border: 1px solid #2A2A29;
  display: inline-flex;
  justify-content: center;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  margin-top: 27px;
  min-width: 191px;
  padding: 7px 10px;
  text-decoration: none;
  text-align: center;
  text-transform: uppercase;
}

.section-our-team {
  background-color: #F4F4F4;
  overflow: hidden;
  padding-bottom: 69px;
  padding-top: 61px;
}

.section-our-team__desc {
  color: #59656F;
  font-weight: 500;
  margin: 19px 0 0 !important;
  max-width: 483px;
}

.section-our-team .gallery__wrap-slider {
  margin-top: 61px;
}

.section-our-team .gallery__wrap-slider .swiper-slide {
  padding-bottom: 0;
}

.section-our-team .gallery__wrap-slider .gallery__navigation {
  margin-top: 64px;
}

.section-our-team .team.only-mobile {
  margin-top: 61px;
}

.certificates {
  background-color: #fff;
  overflow: hidden;
  padding-bottom: 80px;
  padding-top: 112px;
}

.certificates__item p {
  color: #59656F;
  font-weight: 500;
  line-height: 120%;
  margin: 15px 0 0 !important;
}

.certificates__wrap-slider {
  position: relative;
}

.certificates__wrap-slider .swiper-slide img {
  width: 100%;
}

.certificates__wrap-slider .gallery__navigation {
  margin-top: 48px;
  width: 100%;
}

.certificates__wrap-slider .gallery__navigation .swiper-pagination {
  padding: 3px 0 0 20px !important;
  text-align: right;
}

.certificates__wrap-slider .gallery__navigation .swiper-pagination-bullet:last-child {
  margin-right: 0;
}

.faq {
  background-color: #F4F4F4;
  padding-bottom: 112px;
  padding-top: 76px;
}

.faq__top {
  max-width: 630px;
  padding-bottom: 81px;
}

.faq__bottom {
  max-width: 500px;
  padding-top: 78px;
}

.faq__top,
.faq__bottom {
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.faq__top h3,
.faq__bottom h3 {
  font-size: 32px;
  line-height: 130%;
  margin-bottom: -2px;
}

.faq__top p,
.faq__bottom p {
  color: #59656F;
  font-weight: 500;
  margin-top: 27px;
}

.faq__button {
  align-items: center;
  border: 1px solid #2A2A29;
  height: 48px;
  display: inline-flex;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  margin-top: 13px;
  min-width: 191px;
  padding: 7px 10px;
  text-transform: uppercase;
  text-align: center;
  text-decoration: none;
}

.how-to-find-us {
  background-color: #fff;
  overflow: hidden;
  padding-bottom: 100px;
  padding-top: 100px;
}

.how-to-find-us__item {
  width: 100%;
}

.how-to-find-us__item h2 {
  margin-bottom: 27px;
}

.how-to-find-us__item p {
  color: #59656F;
  font-weight: 500;
}

.how-to-find-us__list {
  margin-top: 33px;
}

.how-to-find-us__list li {
  color: #59656F;
  font-weight: 500;
  list-style: none;
  margin: 24px 0;
  padding-left: 49px;
  position: relative;
}

.how-to-find-us__list li::before {
  content: "";
  height: 25px;
  left: 0;
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  width: 25px;
}

.how-to-find-us__list li.tel::before {
  background: url("../images/svg/how-to-find-us-tel-icon.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  top: calc(50% + 2px);
}

.how-to-find-us__list li.email::before {
  background: url("../images/svg/how-to-find-us-email-icon.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  top: calc(50% + 2px);
}

.how-to-find-us__list li.address::before {
  background: url("../images/svg/how-to-find-us-address-icon.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  top: calc(50% - 1px);
}

.how-to-find-us__list li a {
  text-decoration: none;
}

.how-to-find-us__social {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  margin: 29px 0 0 -14px;
}

.how-to-find-us__social li {
  color: #59656F;
  font-weight: 500;
  list-style: none;
  margin: 16px;
  position: relative;
}

.how-to-find-us__social li a::before {
  content: "";
  height: 24px;
  left: 0;
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  width: 24px;
}

.how-to-find-us__social li.fb a::before {
  background: url("../images/svg/how-to-find-us-fb-icon.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.how-to-find-us__social li.yt a::before {
  background: url("../images/svg/how-to-find-us-yt-icon.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.how-to-find-us__social li.is a::before {
  background: url("../images/svg/how-to-find-us-is-icon.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.how-to-find-us__wrap-map {
  position: relative;
  width: 100%;
}

.how-to-find-us__wrap-map iframe {
  border: none;
  left: 0;
  height: 100%;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

/* Page - Service */

.service-page__content {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
  padding-bottom: 105px;
  padding-top: 46px;
}

.service-page__content h1,
.service-page__content h2,
.service-page__content h3,
.service-page__content h4,
.service-page__content h5,
.service-page__content h6 {
  font-weight: 500;
  margin: 0;
  text-align: center;
}

.service-page__content h2,
.service-page__content h3,
.service-page__content h4,
.service-page__content h5,
.service-page__content h6 {
  font-size: 36px;
  margin-bottom: 34px;
  margin-top: 57px;
}

.service-page__content h1 {
  margin-bottom: 64px;
}

.service-page__content li,
.service-page__content p {
  color: #000;
  line-height: 150%;
}

.service-page__content ul,
.service-page__content ol {
  margin-top: 32px;
}

.service-page__content li {
  list-style: none;
  padding-left: 44px;
  position: relative;
}

.service-page__content li::before {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M10.562 15.908L16.958 9.512L16.25 8.804L10.562 14.492L7.712 11.642L7.004 12.35L10.562 15.908ZM12.003 21C10.759 21 9.589 20.764 8.493 20.292C7.39767 19.8193 6.44467 19.178 5.634 18.368C4.82333 17.558 4.18167 16.606 3.709 15.512C3.23633 14.418 3 13.2483 3 12.003C3 10.7577 3.23633 9.58767 3.709 8.493C4.181 7.39767 4.82133 6.44467 5.63 5.634C6.43867 4.82333 7.391 4.18167 8.487 3.709C9.583 3.23633 10.753 3 11.997 3C13.241 3 14.411 3.23633 15.507 3.709C16.6023 4.181 17.5553 4.82167 18.366 5.631C19.1767 6.44033 19.8183 7.39267 20.291 8.488C20.7637 9.58333 21 10.753 21 11.997C21 13.241 20.764 14.411 20.292 15.507C19.82 16.603 19.1787 17.556 18.368 18.366C17.5573 19.176 16.6053 19.8177 15.512 20.291C14.4187 20.7643 13.249 21.0007 12.003 21Z' fill='%23F05222'/%3e%3c/svg%3e");
  background-position: center;
  background-repeat: no-repeat;
  content: "";
  display: block;
  height: 24px;
  left: 0;
  position: absolute;
  top: 1px;
  width: 24px;
}

.service-page__content li:not(:last-child) {
  margin-bottom: 20px;
}

.service-page__content p {
  margin-bottom: 24px !important;
}

.service-page__content p strong {
  font-weight: 600;
}

.service-page__content img {
  display: table;
  margin: 33px auto 0;
}

.service-page__button {
  align-items: center;
  background-color: #00B6DA;
  color: #fff;
  display: flex;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  height: 60px;
  margin: 32px auto 0;
  padding: 7px 10px;
  text-transform: uppercase;
  text-decoration: none;
  text-align: center;
  width: 286px;
}

/* Footer */

.footer {
  background-color: #1A3F52;
  color: #D4DADF;
  overflow: hidden;
  padding-bottom: 75px;
  padding-top: 73px;
  position: relative;
}

.footer .n_container {
  position: relative;
  z-index: 1;
}

.footer__items {
  padding-bottom: 76px;
}

.footer__columns {
  display: flex;
  flex-wrap: wrap;
  margin-left: -15px;
  margin-right: -15px;
}

.footer__item h3 {
  font-size: 16px;
  font-weight: 600;
  line-height: 150%;
  margin-bottom: 17px;
  margin-top: 3px;
}

.footer__column {
  margin-left: 15px;
  margin-right: 15px;
  width: calc(50% - 30px);
}

.footer__columnContent {
  display: table;
  margin-left: auto;
}

.footer__wrapLogo {
  max-height: 46px;
  max-width: 229px;
}

.footer__wrapLogo img,
.footer__wrapLogo svg {
  max-height: 100%;
  max-width: 100%;
}

.footer__blockForm {
  margin-top: 41px;
}

.footer__blockForm .text-above-form {
  font-size: 16px;
  line-height: 150%;
  margin: 0 0 29px !important;
}

.footer__blockForm .text-under-form {
  font-size: 12px;
  line-height: 150%;
  margin: 11px 0 0 !important;
}

.footer__blockForm form {
  align-items: center;
  display: flex;
  height: 45px;
}

.footer__blockForm form input {
  border: none;
  height: 100%;
  font-size: 14px;
  line-height: 22px;
  outline: none;
}

.footer__blockForm form input:not([type=submit]) {
  background-color: #F7F7F7;
  font-weight: 400;
  padding-left: 10px;
  padding-right: 10px;
  width: calc(100% - 12px - 132px);
}

.footer__blockForm form input:not([type=submit])::-moz-placeholder {
  color: #8E90A0;
}

.footer__blockForm form input:not([type=submit])::placeholder {
  color: #8E90A0;
}

.footer__blockForm form input[type=submit] {
  background-color: #00B6DA;
  color: #fff;
  cursor: pointer;
  font-weight: 500;
  margin-left: 12px;
  padding-bottom: 1px;
  text-align: center;
  width: 132px;
}

.footer ul {
  list-style: none;
}

.footer__list {
  margin-top: 24px;
}

.footer__list li {
  font-size: 14px;
  font-weight: 500;
  line-height: 150%;
  margin-bottom: 18px;
  margin-top: 18px;
}

.footer__listContact li {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}

.footer__email,
.footer address {
  font-size: 14px;
  font-weight: 500;
  line-height: 150%;
}

.footer__email {
  display: table;
  margin-top: 14px;
}

.footer address {
  font-style: normal;
  margin-top: 12px;
}

.footer__social {
  display: flex;
  flex-wrap: wrap;
  margin: 11px -6px 0;
}

.footer__social li {
  height: 20px;
  margin: 4px;
  width: 20px;
}

.footer__social li a {
  display: block;
  height: 100%;
  width: 100%;
}

.footer__social li.fb-icon a {
  background: url("../images/fb-icon.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.footer__social li.yt-icon a {
  background: url("../images/yt-icon.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.footer__social li.is-icon a {
  background: url("../images/is-icon.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.footer__social li.ws-icon a {
  background: url("../images/ws-icon.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.footer__social li.tg-icon a {
  background: url("../images/tg-icon.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.footer__social li.vb-icon a {
  background: url("../images/vb-icon.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.footer a {
  color: inherit;
  text-decoration: none;
}

.footer__bottom {
  align-items: center;
  border-top: 1px solid #D4DADF;
  display: flex;
  justify-content: space-between;
  padding-top: 25px;
}

.footer__copyright {
  font-size: 14px;
  line-height: 150%;
  margin: 0 !important;
}

.footer__menu {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.footer__menu li {
  margin: 6px 0 6px 24px;
  text-align: right;
}

.footer__menu li a {
  font-size: 14px;
  line-height: 150%;
}

.footer__element {
  pointer-events: none;
  position: absolute;
  z-index: 0;
}

.footer__element.left {
  left: 0;
  max-width: 22.5%;
  top: 0;
}

.footer__element.right {
  bottom: 0;
  right: 0;
  max-width: 28.5%;
}

/* Plyr video player */

@media screen and (min-width: 481px) {
  .apartments__head .right .select {
    margin-right: 18px;
  }

  .apartments.list .apartment .newest-objects__object {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
  }

  .apartments.list .apartment .newest-objects__object .wrap-info {
    padding-left: 20px;
    width: calc(100% - 220px);
  }

  .apartments.list .apartment .object__blockImage {
    height: 170px;
    padding: 0;
    width: 220px;
  }

  .apartments.list .apartment .object__button {
    margin-top: 10px;
  }

  .team {
    display: flex;
    flex-wrap: wrap;
    margin-left: -12px;
    margin-right: -12px;
  }

  .team__item {
    margin-left: 12px;
    margin-right: 12px;
    width: calc(50% - 24px);
  }

  .section-our-team .team.only-mobile {
    display: none;
  }
}

@media screen and (min-width: 576px) {
  .slider-buttons {
    position: absolute;
    right: 40px;
    transform: translateY(-100%);
    top: -56px;
  }

  .section-blog .tabs__wrap-tab {
    border-bottom: 3px solid #EBEBEB;
  }

  .section-blog .tabs__wrap-tab .tab:not(:first-child) {
    padding-left: 35px;
  }

  .section-blog .tabs__wrap-tab .tab:not(:last-child) {
    padding-right: 35px;
  }

  .section-blog .tabs__wrap-tab .tab {
    border-bottom: 3px solid transparent;
    margin-bottom: -3px;
    padding-bottom: 16px;
    padding-top: 16px;
  }

  .section-blog .tabs__wrap-tab .tab.tab-active {
    border-color: #00B6DA;
  }

  .apartament__socialList,
  .apartament__shortInfoList {
    -moz-column-count: 2;
         column-count: 2;
  }

  .bg-white {
    padding-bottom: 38px;
    padding-top: 33px;
  }

  .section-our-team .gallery__wrap-slider {
    overflow: hidden;
  }
}

@media screen and (min-width: 721px) {
  .section-blog__previewPosts {
    display: flex;
    flex-wrap: wrap;
    margin-left: -7.5px;
    margin-right: -7.5px;
  }

  .section-blog__previewPost {
    margin: 7.5px;
    width: calc(50% - 15px);
  }
}

@media screen and (min-width: 768px) {
  .study-tour__columns {
    display: flex;
    margin-left: -30px;
    margin-right: -30px;
  }

  .study-tour__column {
    margin-left: 30px;
    margin-right: 30px;
    width: calc(50% - 60px);
  }

  .section-blog .tabs__wrap-tab .tab:not(:first-child) {
    padding-left: 65px;
  }

  .section-blog .tabs__wrap-tab .tab:not(:last-child) {
    padding-right: 65px;
  }

  .apartments.list .apartment .newest-objects__object .wrap-info {
    padding-left: 32px;
    width: calc(100% - 260px);
  }

  .apartments.list .apartment .object__blockImage {
    height: 150px;
    width: 260px;
  }

  .apartments.list .apartment .object__list {
    display: flex;
    justify-content: space-between;
    margin-left: -5px;
    margin-right: -5px;
    max-width: 563px;
  }

  .apartments.list .apartment .object__list li {
    display: block;
    margin-left: 5px;
    margin-right: 5px;
  }

  .apartments.list .apartment .object__list li::before {
    transform: translateY(0);
    top: -1px;
  }

  .apartments.list .apartment .object__list li strong {
    display: table;
    margin: 13px 0 0;
    padding: 0;
  }

  .apartament__wrap-slider {
    display: flex;
    flex-wrap: wrap;
    height: 800px;
    margin: 29px -10px 0;
  }

  .apartament__big-slider {
    margin-left: 10px;
    margin-right: 10px;
    width: calc(72% - 21px);
  }

  .apartament__gallery-slider {
    margin-left: 10px;
    margin-right: 10px;
    width: calc(28% - 19px);
  }

  .team {
    margin-left: -16px;
    margin-right: -16px;
  }

  .team__item {
    margin-left: 16px;
    margin-right: 16px;
    width: calc(33.3333% - 32px);
  }
}

@media screen and (min-width: 801px) {
  .apartament__page-head {
    align-items: center;
    display: flex;
  }

  .apartament__page-head .left {
    padding-right: 30px;
    width: 54%;
  }

  .apartament__page-head .right {
    width: 46%;
  }
}

@media screen and (min-width: 861px) {
  .uptrends-offer__items {
    display: flex;
    flex-wrap: wrap;
    margin-left: -20px;
    margin-right: -20px;
  }

  .uptrends-offer__item {
    margin-left: 20px;
    margin-right: 20px;
    width: calc(50% - 40px);
  }

  .our-customers__items {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
  }

  .our-customers__item:nth-child(1) {
    width: calc(47% - 3px);
  }

  .our-customers__item:nth-child(2) {
    padding-left: 50px;
    width: calc(53% + 3px);
  }

  .our-customers__blockImage {
    height: 640px;
  }

  .how-to-find-us__items {
    display: flex;
    flex-wrap: wrap;
  }

  .how-to-find-us__item {
    width: 50%;
  }

  .how-to-find-us__item:nth-child(1) {
    padding-right: 50px;
  }

  .how-to-find-us__item h2 {
    margin-top: -4px;
  }

  .how-to-find-us__wrap-map {
    height: 560px;
  }
}

@media screen and (min-width: 961px) {
  .section-blog__previewPost {
    width: calc(33.3333% - 15px);
  }

  .apartament__items {
    display: flex;
    flex-wrap: wrap;
  }

  .apartament__item:nth-child(1) {
    padding-right: 40px;
    width: calc(100% - 355px);
  }

  .apartament__item:nth-child(2) {
    width: 355px;
  }

  .apartament__sidebar {
    padding-left: 21px;
  }

  .apartament__sidebar::before {
    background-color: #E1E4E6;
    bottom: 0;
    content: "";
    height: calc(100% - 17px);
    display: block;
    left: 0;
    position: absolute;
    top: 60px;
    width: 2px;
  }

  .apartament__widget {
    padding-left: 24px;
    padding-right: 24px;
  }

  .apartament__widget:not(:first-child) {
    margin-top: 49px;
  }

  .about-company__items {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    margin-left: -40px;
    margin-right: -40px;
  }

  .about-company__item {
    margin-left: 40px;
    margin-right: 40px;
    width: calc(50% - 80px);
  }

  .certificates__items {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
  }

  .certificates__item:nth-child(1) {
    padding-right: 40px;
    width: 56%;
  }

  .certificates__item:nth-child(1) .certificates__content {
    margin-top: -20%;
    position: relative;
    z-index: 3;
  }

  .certificates__item:nth-child(2) {
    width: 44%;
  }

  .certificates__item p {
    max-width: 483px;
  }

  .certificates__wrap-slider::before {
    background-color: #fff;
    bottom: 0;
    content: "";
    display: block;
    height: 100%;
    left: 0;
    pointer-events: none;
    position: absolute;
    transform: translateX(-100%);
    top: 0;
    width: 100vw;
    z-index: 2;
  }
}

@media screen and (min-width: 992px) {
  .contact-us__desc {
    max-width: 483px;
  }

  .contact-us__items {
    display: flex;
    flex-wrap: wrap;
    margin-left: -25px;
    margin-right: -25px;
  }

  .contact-us__item {
    margin-left: 25px;
    margin-right: 25px;
  }

  .contact-us__item:nth-child(1) {
    width: calc(51% - 48px);
  }

  .contact-us__item:nth-child(2) {
    width: calc(49% - 52px);
  }

  .apartments.list .apartment .newest-objects__object {
    flex-wrap: nowrap;
  }

  .apartments.list .apartment .newest-objects__object .wrap-info {
    padding-left: 32px;
    padding-right: 32px;
    width: calc(100% - 260px - 220px);
  }

  .apartments.list .apartment .object__button {
    margin: 20px 0 auto;
    width: 220px;
  }

  .footer__items {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
  }

  .footer__item:nth-child(1) {
    padding-right: 40px;
    width: calc(60% - 5px);
  }

  .footer__item:nth-child(2) {
    width: calc(40% + 5px);
  }

  .footer__blockForm {
    max-width: 500px;
  }

  .footer__email {
    word-break: break-all;
  }
}

@media screen and (min-width: 1025px) {
  body::-webkit-scrollbar {
    width: 8px;
  }

  body::-webkit-scrollbar-track {
    background-color: #e7e7e7;
  }

  body::-webkit-scrollbar-thumb {
    background-color: #bebebe;
  }
}

@media screen and (min-width: 1100px) {
  .study-tour__columns {
    margin-left: -80px;
    margin-right: -80px;
  }

  .study-tour__column {
    margin-left: 80px;
    margin-right: 80px;
    padding-right: 7px;
    width: calc(50% - 160px);
  }
}

@media screen and (min-width: 1101px) {
  .header__phone {
    margin-left: 14px;
  }

  .header__icons li:last-child {
    margin-right: 0;
  }

  body._touch .header .menu__list > li {
    align-items: center;
    display: flex;
  }

  body._touch .header .menu__list > li._active .sub-menu {
    opacity: 1;
    pointer-events: all;
    transform: translate(0, 0);
    visibility: visible;
  }

  .team__item {
    width: calc(25% - 32px);
  }

  .our-customers__item:nth-child(2) {
    padding-left: 80px;
  }

  .how-to-find-us__wrap-map {
    height: 734px;
  }
}

@media (min-width: 1101px) {
  .header .menu__body-bottom {
    display: none;
  }

  .header .menu {
    margin-left: auto;
    padding-bottom: 12px;
    padding-top: 9px;
    margin-bottom: -3px;
    width: auto;
  }

  .header .menu__list {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-left: 15px;
  }

  .header .menu__list > li {
    position: relative;
  }

  .header .menu__list li {
    color: #2F363C;
  }

  .header .menu__list li:hover,
  .header .menu__list li.current-menu-item,
  .header .menu__list li.current_page_item {
    color: #00b6da;
  }

  .header .menu__list li a {
    color: inherit !important;
  }

  .header .menu__list > li {
    align-items: center;
    display: flex;
    font-size: 14px;
    font-weight: 500;
    line-height: 120%;
    margin-left: 18px;
    margin-right: 18px;
    padding-bottom: 6px;
    padding-top: 6px;
  }

  .header .menu__list > li:first-child {
    margin-left: 0;
  }

  .header .menu__list > li:last-child {
    margin-right: 0;
  }

  .header .menu__list > li.menu-item-has-children {
    padding-right: 12px;
  }

  .header .sub-menu {
    left: calc(50% - 100px);
    position: absolute;
    left: -20px;
    top: 100%;
    z-index: 10;
    margin: 0;
    padding: 10px 20px;
    width: 180px;
    opacity: 0;
    transition: 0.3s ease 0s;
    visibility: hidden;
  }

  .header .sub-menu::after {
    background-color: #fff;
    bottom: 0;
    content: "";
    left: 0;
    height: 100%;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 7px;
    width: 100%;
    z-index: -1;
  }

  .header .sub-menu li {
    color: #2F363C;
    display: table;
    text-transform: none;
  }

  .header .sub-menu li:not(:last-child) {
    margin-bottom: 16px;
  }

  .header .sub-menu li:hover,
  .header .sub-menu li.current-menu-item,
  .header .sub-menu li.current_page_item {
    color: #00b6da !important;
  }

  .header .sub-menu li a {
    color: inherit !important;
  }
}

@media (min-width: 1101px) and (any-hover: hover) {
  .header .menu__list li:hover {
    color: #00b6da;
  }

  .header .menu__list li:hover a {
    color: inherit;
  }
}

@media screen and (min-width: 1361px) {
  .most-popular .newest-objects__slider {
    max-width: 91%;
  }
}

@media screen and (min-width: 1921px) {
  .first-screen__bg {
    left: 50%;
    margin-left: auto;
    margin-right: auto;
    max-width: 1920px;
    transform: translateX(-50%);
  }
}

@media screen and (max-width: 1920px) {
  .first-screen__bg {
    left: 0;
    -o-object-fit: cover;
       object-fit: cover;
    -o-object-position: 0 60%;
       object-position: 0 60%;
  }
}

@media screen and (max-width: 1440px) {
  .first-screen__bg {
    -o-object-position: bottom;
       object-position: bottom;
  }
}

@media screen and (max-width: 1360px) {
  .newest-objects__slider {
    margin-left: -40px;
    margin-right: -40px;
    padding-left: 40px;
    padding-right: 40px;
  }
}

@media (max-width: 1320px) {
  .popup__body {
    padding-right: calc(14px + 26 * ((100vw - 320px) / 1000));
  }

  .popup__body {
    padding-left: calc(14px + 26 * ((100vw - 320px) / 1000));
  }

  .popup-video .popup__content {
    height: calc(180px + 212 * ((100vw - 320px) / 1000));
  }

  .popup-video .popup__content {
    width: calc(335px + 360 * ((100vw - 320px) / 1000));
  }
}

@media screen and (max-width: 1280px) {
  .header__icons {
    margin-left: 50px;
  }

  .first-screen {
    padding-bottom: 533px;
    padding-top: 126px;
  }

  .first-screen__bg {
    -o-object-position: top;
       object-position: top;
  }

  .search-form__content {
    flex-wrap: wrap;
  }

  .search-form__column {
    width: calc(25% - 17px) !important;
  }

  .search-form__submit {
    margin: 20px 9px 0 auto;
  }
}

@media screen and (max-width: 1200px) {
  .section-objects .tabs__scroll::after {
    margin-right: -40px;
    padding-right: 40px;
    width: calc(100% + 40px);
  }
}

@media screen and (max-width: 1100px) {
  .header-container {
    align-items: center;
    height: 80px;
    display: flex;
    justify-content: space-between;
  }

  .header__top {
    display: none;
  }

  .header__wrap-social-and-phone {
    background-color: #F1F3F5;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 128px;
    margin-left: -40px;
    margin-right: -40px;
    padding-left: 40px;
    padding-right: 40px;
    width: calc(100% + 80px);
  }

  .header__wrap-social-and-phone.hidden {
    display: none;
    opacity: 0;
  }

  .header__social {
    margin-bottom: 13px;
  }

  .header__social li {
    height: 22px;
    margin: 9px;
    width: 22px;
  }

  .header__social li.ws-icon a {
    background: url("../images/svg/ws-header-icon-mobile.svg");
  }

  .header__social li.tg-icon a {
    background: url("../images/svg/tg-header-icon-mobile.svg");
  }

  .header__social li.vb-icon a {
    background: url("../images/svg/vb-header-icon-mobile.svg");
  }

  .header__phone {
    font-size: 16px;
  }

  .header__icons {
    margin-left: 0;
  }

  .header__icons li.search-icon {
    display: none;
  }

  .header__blockForm {
    bottom: 110px;
    bottom: 110px;
    top: auto;
    padding-bottom: 10px;
    padding-top: 5px;
  }

  .header__blockForm.show {
    top: auto;
  }

  .language {
    margin-left: 6px;
  }

  body._touch .header .menu__list > li._active .sub-menu {
    display: block;
  }

  .uptrends-offer__list li br {
    display: none;
  }

  .testimonials__aboveTitle,
  .testimonials__title {
    padding-right: 110px;
  }

  .slider-buttons {
    right: 0;
  }

  .study-tour__button {
    margin-top: 20px;
  }

  .section-blog .tabs__wrap-tab .tab {
    font-size: 38px;
  }
}

@media (max-width: 1100px) {
  .header {
    /*
    .menu__icon._active span {
       transform: scale(0) translate(0, -50%);
    }
    .menu__icon._active::before {
       transform: rotate(-45deg) translate(0, -70%);
       top: 50%;
    }
    .menu__icon._active::after {
       bottom: 50%;
       transform: rotate(45deg) translate(0, 70%);
    }
    .menu__icon._active span,
    .menu__icon._active::before,
    .menu__icon._active::after {
       background-color: #000;
    }
    */
  }

  .header .menu__icon {
    display: block;
    height: 12px;
    margin-left: 17px;
    position: relative;
    flex: 0 0 40px;
    outline: none;
    z-index: 5;
  }

  .header .menu__icon span,
  .header .menu__icon::before,
  .header .menu__icon::after {
    background-color: #2F363C;
    border-radius: 6px;
    left: 0;
    height: 1px;
    position: absolute;
    transition: 0.2s;
    width: 100%;
  }

  .header .menu__icon::before,
  .header .menu__icon::after {
    content: "";
  }

  .header .menu__icon::before {
    top: 0;
  }

  .header .menu__icon::after {
    bottom: 0;
    left: auto;
    right: 0;
    width: 22px;
  }

  .header .menu__icon span {
    font-size: 0;
    opacity: 0;
    transform: scale(1) translate(0, -50%);
    top: 50%;
  }

  .header .menu__body {
    background-color: #fff;
    left: 0;
    opacity: 0;
    padding-left: 40px;
    padding-right: 40px;
    pointer-events: none;
    position: fixed;
    right: 0;
    transition: 0.3s;
    top: 0;
    width: 100%;
    z-index: 5;
  }

  .header .menu__body._active {
    opacity: 1;
    pointer-events: all;
  }

  .header .menu__body-bottom {
    margin-bottom: 48px;
  }

  .header .menu__body-bottom ul {
    display: flex;
    justify-content: center;
    margin: 0 -14px;
  }

  .header .menu__body-bottom li {
    align-items: center;
    background-color: #F8F8F8;
    height: 66px;
    display: flex;
    justify-content: center;
    margin-left: 14px;
    margin-right: 14px;
    width: 66px;
  }

  .header .menu__body-bottom li .select-currency,
  .header .menu__body-bottom li .language {
    margin: 0;
  }

  .header .menu__body-bottom li .select-currency__icon,
  .header .menu__body-bottom li .language__icon {
    margin: 0 0 9px;
  }

  .header .menu__body-bottom li .select-currency__header,
  .header .menu__body-bottom li .language__header {
    align-items: center;
    background-color: #F8F8F8;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    width: 100%;
  }

  .header .menu__body-bottom li .select-currency .select__body,
  .header .menu__body-bottom li .select-currency__body,
  .header .menu__body-bottom li .language .select__body,
  .header .menu__body-bottom li .language__body {
    background-color: #F8F8F8;
    bottom: auto;
    left: -13px;
    transform: translateY(-100%);
    top: 0;
    width: 66px;
  }

  .header .menu-topmenu-container {
    box-sizing: border-box;
    height: calc(100dvh - 80px - 242px);
    margin-top: 80px;
    padding: 12px 0 20px;
    overflow: auto;
  }

  .header .menu__list {
    padding: 0;
    overflow-x: hidden;
  }

  .header .menu__list .menu-item {
    width: 100%;
    max-width: 100%;
  }

  .header .menu__list .menu-item a {
    position: relative;
  }

  .header .menu__list > li.current-menu-item,
  .header .menu__list > li.current_page_item {
    color: #00b6da;
  }

  .header .menu__list > li.current-menu-item::after,
  .header .menu__list > li.current_page_item::after {
    display: none;
  }

  .header .menu__list > li.menu-item-has-children > a {
    align-items: center;
    display: flex;
    justify-content: center;
    position: relative;
  }

  .header .menu__list > li.menu-item-has-children > a::after {
    bottom: 1px;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 4px solid black;
    content: "";
    height: 0;
    margin-left: 11px;
    position: relative;
    width: 0;
    z-index: 0;
  }

  .header .menu__list > li.menu-item-has-children._active a::after {
    transform: scale(1, -1);
  }

  .header .menu__list > li a {
    color: inherit;
    display: table;
    margin: 26px auto;
  }

  .header .menu__list li {
    display: table;
    color: #2F363C;
    font-size: 16px;
    font-weight: 500;
    line-height: 100%;
  }

  .header .sub-menu {
    align-items: center;
    display: none;
    flex: 1 1 100%;
    margin-top: 25px;
    position: relative;
    width: 100%;
  }

  .header .sub-menu li {
    padding-left: 0;
  }

  .header .sub-menu li.current-menu-item,
  .header .sub-menu li.current_page_item {
    color: #00b6da;
  }

  .header .sub-menu li:last-child {
    padding-bottom: 0;
  }
}

@media screen and (max-width: 1100px) and (max-width: 1100px) {
  .header .menu__icon {
    order: 1;
  }
}

@media screen and (max-width: 1100px) and (max-width: 575.98px) {
  .header .menu__icon {
    flex: 0 0 34px;
  }

  .header .menu__icon::after {
    width: 16px;
  }

  .header .menu__body {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

@media screen and (max-width: 1100px) and (max-width: 1024px) {
  .header .menu__body {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media (max-width: 1100px) and (max-height: 575px) {
  .header .menu__body-bottom {
    margin-bottom: 5px;
  }

  .header .menu__body-bottom ul {
    margin: 0 -5px;
  }

  .header .menu__body-bottom li {
    height: 45px;
    margin-left: 5px;
    margin-right: 5px;
    width: 45px;
  }

  .header .menu__body-bottom li .select-currency__icon,
  .header .menu__body-bottom li .language__icon {
    margin-bottom: 3px;
  }

  .header .menu__body-bottom li .select-currency .select__body,
  .header .menu__body-bottom li .select-currency__body,
  .header .menu__body-bottom li .language .select__body,
  .header .menu__body-bottom li .language__body {
    left: -5px;
    width: 45px;
  }

  .header .menu__body-bottom li .select-currency .select__body .select__item,
  .header .menu__body-bottom li .select-currency__body .select__item,
  .header .menu__body-bottom li .language .select__body .select__item,
  .header .menu__body-bottom li .language__body .select__item {
    padding-left: 3px;
    padding-right: 3px;
  }

  .header .menu-topmenu-container {
    height: calc(100dvh - 80px - 102px);
    padding: 0 0 10px;
  }

  .header .menu__list > li a {
    margin: 12px auto;
  }

  .header .sub-menu {
    margin-top: 11px;
  }
}

@media screen and (max-width: 1100px) and (max-width: 360.98px) {
  .header .menu__body-bottom ul {
    margin: 0 -8px;
  }

  .header .menu__body-bottom li {
    margin-left: 8px;
    margin-right: 8px;
  }
}

@media screen and (max-width: 1024px) {
  .n_container {
    padding-left: 30px;
    padding-right: 30px;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: 32px;
  }

  .header-container {
    padding-left: 30px;
    padding-right: 30px;
  }

  .header__wrap-social-and-phone {
    margin-left: -30px;
    margin-right: -30px;
    padding-left: 30px;
    padding-right: 30px;
    width: calc(100% + 60px);
  }

  .search-form__content.all-fields .search-form__column {
    width: calc(25% - 17px);
  }

  .section-objects__left {
    width: 50%;
  }

  .section-objects__right {
    padding-left: 20px;
    width: 50%;
  }

  .section-objects .tabs__scroll::after {
    margin-right: -30px;
    padding-right: 30px;
    width: calc(100% + 30px);
  }

  .section-objects .tabs__wrap-tab {
    margin-right: -30px;
    padding-right: 30px;
    width: calc(100% + 30px);
  }

  .study-tour {
    padding-bottom: 0;
    padding-top: 0;
  }

  .study-tour .n_container {
    padding-left: 0;
    padding-right: 0;
  }

  .study-tour__block {
    border-radius: 0;
    padding: 40px 35px;
  }

  .study-tour__columns {
    padding-bottom: 25px;
    margin-top: 34px;
  }

  .study-tour.team-page .n_container {
    padding-left: 30px;
    padding-right: 30px;
  }

  .newest-objects__slider {
    margin-left: -30px;
    margin-right: -30px;
    padding-left: 30px;
    padding-right: 30px;
  }

  .section-blog .tabs__wrap-tab .tab {
    font-size: 34px;
  }

  .page__title {
    font-size: 32px;
    font-size: 32px;
    font-weight: 500;
    line-height: 120%;
    margin-top: 58px;
  }

  .page__title br {
    display: none;
  }

  .apartments.grid .apartment {
    width: calc(33.3333% - 45px);
  }

  .apartament__page-head h1 {
    font-size: 36px;
    margin-left: auto;
    margin-right: auto;
    max-width: 540px;
  }
}

@media screen and (max-width: 991px) {
  .contact-us__item:nth-child(2) {
    padding-top: 50px;
  }

  .contact-us__blockImage {
    padding-bottom: calc(92% + 1px);
  }

  .footer__columns {
    margin-left: -10px;
    margin-right: -10px;
    margin-top: 58px;
  }

  .footer__column {
    margin-left: 10px;
    margin-right: 10px;
  }

  .footer__column:nth-child(1) {
    width: calc(42% - 28px);
  }

  .footer__column:nth-child(2) {
    width: calc(58% - 32px);
  }
}

@media screen and (max-width: 960px) {
  .popup__content h2 {
    font-size: 32px;
  }

  .apartament__sidebar {
    padding-top: 140px;
  }

  .apartament__widget.want-to-know-more {
    margin-bottom: 48px;
  }

  .apartament__widget.managers {
    margin: 0 auto 0;
    max-width: 286px;
    text-align: center;
  }

  .about-company__item:nth-child(2) {
    margin-top: 51px;
  }

  .certificates__item:nth-child(2) {
    margin-top: 78px;
  }

  .certificates__wrap-slider .gallery__navigation .swiper-pagination {
    padding-top: 5px !important;
  }
}

@media screen and (max-width: 860px) {
  .uptrends-offer__item:nth-child(2) {
    margin-top: 50px;
  }

  .our-customers__blockImage {
    padding-bottom: calc(64% + 3px);
    margin-bottom: 58px;
  }

  .how-to-find-us__item:nth-child(2) {
    margin-top: 28px;
  }

  .how-to-find-us__wrap-map {
    height: auto;
    padding-bottom: 82%;
  }
}

@media screen and (max-width: 800.98px) {
  .apartments.grid .apartment {
    width: calc(50% - 45px);
  }

  .apartament__page-head .right {
    text-align: center;
  }

  .apartament__page-head h1 {
    text-align: center;
  }

  .apartament__page-head ul {
    justify-content: center;
  }

  .apartament__page-head .price {
    justify-content: center;
    margin-top: 35px !important;
  }
}

@media screen and (max-width: 767.98px) {
  .search-form__content.all-fields .search-form__column {
    width: calc(33.3333% - 17px);
  }

  .search-form__column {
    width: calc(33.3333% - 17px) !important;
  }

  .study-tour__columns {
    padding-bottom: 0;
  }

  .section-blog .tabs__wrap-tab .tab {
    font-size: 28px;
  }

  .apartament__wrap-slider {
    margin-top: 27px;
  }

  .apartament__big-slider .swiper-slide {
    padding-bottom: 78%;
  }

  .apartament__gallery-slider {
    margin-top: 20px;
  }

  .apartament__gallery-slider .swiper-slide {
    padding-bottom: 43%;
  }

  .apartament__gallery-slider .swiper-slide:nth-child(odd) {
    width: calc(46% - 10px);
  }

  .apartament__gallery-slider .swiper-slide:nth-child(even) {
    width: calc(54% - 10px);
  }

  .service-page__content h1 br,
  .service-page__content h2 br,
  .service-page__content h3 br,
  .service-page__content h4 br,
  .service-page__content h5 br,
  .service-page__content h6 br {
    display: none;
  }
}

@media screen and (max-width: 720.98px) {
  .section-objects .tabs {
    margin-top: 20px;
  }

  .section-objects .tabs__wrap-tab .tab {
    padding: 13px 11px;
  }

  .section-objects .tabs__wrap-tab .tab:first-child {
    padding-left: 0;
  }

  .section-objects .tabs__wrap-tab .tab:last-child {
    padding-right: 0;
  }

  .study-tour__block {
    padding-left: 25px;
    padding-right: 25px;
  }

  .section-blog__previewPost:not(:last-child) {
    margin-bottom: 16px;
  }

  .apartments__head {
    align-items: flex-end;
  }

  .apartments.grid {
    margin-left: -15px;
    margin-right: -15px;
  }

  .apartments.grid .apartment {
    margin: 0 15px 30px;
    width: calc(50% - 30px);
  }

  .service-page__content h2,
  .service-page__content h3,
  .service-page__content h4,
  .service-page__content h5,
  .service-page__content h6 {
    font-size: 30px;
  }
}

@media screen and (max-width: 640.98px) {
  .search-form__content.all-fields .search-form__column {
    width: calc(50% - 17px);
  }

  .search-form__column {
    width: calc(50% - 17px) !important;
  }

  .section-objects__left {
    width: 100%;
  }

  .section-objects__right {
    display: none;
  }
}

@media screen and (max-width: 575.98px) {
  body p:not(:last-child) {
    margin-bottom: 12px;
  }

  .breadcrumbs {
    margin-bottom: 33px;
    padding-top: 7px;
  }

  .breadcrumbs__item {
    font-size: 8px;
    line-height: 100%;
  }

  .breadcrumbs__item:not(:last-child)::after {
    bottom: 3px;
    margin: 0 8px 0 5px;
  }

  .n_container {
    padding-left: 15px;
    padding-right: 15px;
  }

  h1 br,
  h2 br,
  h3 br,
  h4 br,
  h5 br,
  h6 br {
    display: none;
  }

  .block__content p {
    margin-bottom: 15px !important;
  }

  .block__content ol {
    padding-left: 22px;
  }

  .popup__content {
    padding: 31px 16px;
  }

  .popup__content h2 {
    font-size: 24px;
    line-height: 120%;
    margin-bottom: 23px;
  }

  .popup__content p {
    font-size: 14px;
    line-height: 120%;
  }

  .popup__content .contact-us__form {
    margin-top: 24px;
  }

  .popup__close {
    right: 15px;
    top: 32px;
  }

  .popup-custom-checkbox {
    line-height: 18px;
    margin-bottom: 7px;
    margin-top: 22px;
  }

  .header-container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .header__wrap-social-and-phone {
    margin-left: -15px;
    margin-right: -15px;
    padding-left: 15px;
    padding-right: 15px;
    width: calc(100% + 30px);
  }

  .header__blockLogo {
    bottom: 4px;
    position: relative;
  }

  .header__icons li {
    margin-left: 0;
  }

  .header__blockForm form input[type=search] {
    width: calc(100% - 105px);
  }

  .header__blockForm form button[type=submit] {
    margin-left: 5px;
  }

  .first-screen h1 {
    font-size: 40px;
    line-height: 111%;
  }

  .first-screen p {
    font-size: 14px;
    margin-top: 26px;
    padding-right: 0;
  }

  .first-screen__wrap-button {
    margin-top: 24px;
  }

  .first-screen__bg {
    -o-object-position: bottom;
       object-position: bottom;
  }

  .block-search-form .tabs {
    margin-top: -187px;
  }

  .most-popular {
    padding-bottom: 31px;
    padding-top: 98px;
  }

  .most-popular__desc {
    margin-top: 16px;
  }

  .most-popular .newest-objects__slider {
    margin-top: 61px;
  }

  .most-popular__button {
    display: none;
  }

  .uptrends-offer__item p {
    margin-top: 17px;
  }

  .uptrends-offer__list li:not(:last-child) {
    margin-bottom: 23px;
  }

  .save-up-to {
    padding-bottom: 58px;
    padding-top: 56px;
  }

  .save-up-to__title {
    font-size: 28px;
  }

  .save-up-to__button {
    margin-top: 33px;
  }

  .section-objects {
    padding-bottom: 100px;
    padding-top: 88px;
  }

  .section-objects .tabs__scroll::after {
    margin-right: -15px;
    padding-right: 15px;
    width: calc(100% + 15px);
  }

  .section-objects .tabs__wrap-tab {
    margin-right: -15px;
    padding-right: 15px;
    width: calc(100% + 15px);
  }

  .testimonials {
    padding-bottom: 48px;
    padding-top: 26px;
  }

  .testimonials__aboveTitle,
  .testimonials__title {
    padding-right: 0;
  }

  .testimonials__aboveTitle {
    margin-bottom: 13px;
  }

  .testimonials__title {
    max-width: 345px;
  }

  .testimonials__slider {
    margin-top: 34px;
  }

  .testimonials__button {
    display: none;
  }

  .slider-buttons {
    margin-top: 32px;
  }

  .study-tour__block {
    padding: 34px 16px 41px;
  }

  .study-tour__button {
    margin-top: 1px;
    padding-left: 12px;
  }

  .study-tour.team-page {
    padding-bottom: 80px;
    padding-top: 48px;
  }

  .study-tour.team-page .n_container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .study-tour.team-page .study-tour__block {
    padding-bottom: 59px;
    padding-top: 58px;
  }

  .study-tour.team-page .study-tour__content {
    margin-top: 48px;
    padding-bottom: 32px;
  }

  .top10 {
    padding-bottom: 58px;
    padding-top: 46px;
  }

  .top10__title {
    font-size: 32px;
    margin-left: auto;
    margin-right: auto;
    max-width: 280px;
  }

  .top10__desc {
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    max-width: 250px;
  }

  .top10__buttons {
    display: block;
    font-size: 14px;
    margin: 32px auto 0;
    max-width: 191px;
  }

  .top10__button {
    margin: 0;
    width: 100%;
  }

  .top10__button.color {
    margin-bottom: 13px;
  }

  .newest-objects {
    padding-bottom: 52px;
    padding-top: 98px;
  }

  .newest-objects__desc {
    margin-top: 16px;
  }

  .newest-objects__slider {
    margin-top: 50px;
    margin-left: -15px;
    margin-right: -15px;
    padding-left: 15px;
    padding-right: 15px;
  }

  .newest-objects__button {
    display: none;
  }

  .section-video {
    padding-bottom: 58px;
    padding-top: 48px;
  }

  .section-video .testimonials__title {
    text-align: center;
  }

  .section-video .section-blog__previewPosts {
    margin-top: 33px;
  }

  .section-video .section-blog__previewPost {
    padding: 7px;
  }

  .section-video .section-blog__previewPost .previewPost-blockImage {
    height: 100px;
    width: 100px;
  }

  .section-video .section-blog__previewPost .previewPost-blockInfo {
    width: calc(100% - 100px);
  }

  .section-video .section-blog__previewPost .previewPost-blockInfoBottom {
    margin-bottom: 0;
  }

  .section-video .section-blog__previewPost .previewPost-blockInfoBottom p,
  .section-video .section-blog__previewPost .previewPost-blockInfoBottom a {
    margin-bottom: 0 !important;
  }

  .section-video__button {
    display: none;
  }

  .section-blog {
    padding-bottom: 50px;
  }

  .section-blog .tabs__wrap-tab .tab {
    display: none;
    font-size: 32px;
  }

  .section-blog .tabs__wrap-tab .tab.tab-active {
    display: block;
    text-align: center;
  }

  .section-blog .tabContent.content-active {
    padding-top: 33px;
  }

  .section-blog__previewPost {
    padding: 7px 7px 3px;
  }

  .section-blog__button {
    display: none;
  }

  .contact-us {
    padding-bottom: 53px;
    padding-top: 99px;
  }

  .contact-us__desc {
    margin-top: 17px !important;
  }

  .page__desc {
    margin: 16px auto -11px !important;
    max-width: 338px;
  }

  .apartments__head {
    margin-top: 29px;
  }

  .apartament__page-head h1 {
    font-size: 32px;
    line-height: 110%;
  }

  .apartament__page-head ul {
    margin-top: 19px;
  }

  .apartament__page-head ul li.favorite {
    padding-left: 5px;
  }

  .apartament__page-head ul li.favorite::before {
    height: 12px;
    margin-right: 3px;
    width: 12px;
  }

  .apartament__page-head ul li.share::before {
    height: 12px;
    margin-right: 3px;
    width: 12px;
  }

  .apartament__page-head ul li.print::before {
    height: 12px;
    margin-right: 3px;
    width: 12px;
  }

  .apartament__page-head ul li a {
    font-size: 10px;
    line-height: 100%;
    height: 20px;
    padding: 1px 4px 3px;
  }

  .apartament__page-head .price {
    font-size: 28px;
    line-height: 100%;
    margin-top: 19px;
  }

  .apartament__page-head .id {
    margin-top: 17px !important;
  }

  .apartament__big-slider .tags {
    left: 12px;
    top: 13px;
  }

  .apartament__big-slider .tags .tag {
    font-size: 12.22px;
    line-height: 100%;
    padding: 5px 10px;
  }

  .apartament__big-slider .tags .tag:not(:last-child) {
    margin-right: 7px;
  }

  .apartament__gallery-slider {
    margin-top: 10px;
  }

  .apartament__gallery-slider .swiper-slide:nth-child(odd) {
    width: calc(46% - 6.5px);
  }

  .apartament__gallery-slider .swiper-slide:nth-child(even) {
    width: calc(54% - 2.5px);
  }

  .apartament__items {
    padding-top: 38px;
  }

  .apartament__itemBlock p {
    line-height: 20px;
  }

  .apartament__itemBlock p:not(:last-child) {
    margin-bottom: 25px;
  }

  .relevant-objects {
    margin-bottom: -38px;
    padding-top: 80px;
  }

  .relevant-objects h2 {
    font-weight: 500;
  }

  .relevant-objects__desc {
    font-size: 14px;
    margin-top: 17px;
    max-width: 280px;
  }

  .managers-wrap-slider .slider-buttons {
    display: none;
  }

  .managers-slider__image {
    height: 200px;
    width: 220px;
  }

  .apartament-big-slider-show-all-photos {
    bottom: 10px;
    left: 9px;
  }

  .apartament-big-slider-show-all-photos span::before {
    bottom: 0;
  }

  .apartament-wrap-buttons-swiper {
    bottom: 10px;
    right: 10px;
    transform: translateX(2px);
  }

  .team-page__head .page__title {
    margin-top: -7px;
  }

  .team-page__head .page__desc {
    margin-top: 17px !important;
  }

  .team {
    margin-top: 44px;
  }

  .about-page {
    margin-top: -2px;
  }

  .about-page > .n_container .about-company__items .about-company__item p {
    font-size: 16px;
  }

  .about-company__items {
    padding-bottom: 60px;
    padding-top: 60px;
  }

  .about-company__item h1 {
    margin: 0 0 24px;
  }

  .about-company__pageImage {
    padding-bottom: calc(131% - 2px);
  }

  .about-company__counters {
    padding-top: 35px;
  }

  .about-company__counter:nth-child(odd) {
    padding-left: 0;
    padding-right: 15px;
  }

  .about-company__counter:nth-child(even) {
    padding-left: 15px;
    padding-right: 0;
  }

  .gallery {
    padding-bottom: 60px;
    padding-top: 58px;
  }

  .gallery__wrap-slider {
    margin-top: 39px;
  }

  .gallery__wrap-slider .swiper-slide {
    padding-bottom: calc(84% - 2px);
  }

  .gallery__navigation {
    margin-top: 48px;
  }

  .our-customers {
    padding-bottom: 60px;
    padding-top: 60px;
  }

  .our-customers__item h2 {
    margin-bottom: 40px;
  }

  .section-our-team {
    padding-bottom: 28px;
    padding-top: 58px;
  }

  .section-our-team__desc {
    margin-top: 17px !important;
  }

  .section-our-team .gallery__wrap-slider {
    margin: 33px -15px 0;
    padding: 0 15px 35px;
    width: calc(100% + 30px);
  }

  .section-our-team .team.only-mobile {
    margin-top: 33px;
  }

  .certificates {
    padding-bottom: 60px;
    padding-top: 58px;
  }

  .certificates__item p {
    margin-top: 13px !important;
  }

  .faq {
    padding-bottom: 60px;
    padding-top: 58px;
  }

  .faq__bottom {
    padding-top: 77px;
  }

  .faq__top,
  .faq__bottom {
    max-width: 318px;
  }

  .faq__button {
    margin-top: 21px;
  }

  .how-to-find-us {
    padding-bottom: 60px;
    padding-top: 58px;
  }

  .how-to-find-us__item h2 {
    margin-bottom: 24px;
  }

  .how-to-find-us__item p br {
    display: none;
  }

  .how-to-find-us__list li br {
    display: none;
  }

  .how-to-find-us__social {
    margin-top: 28px;
  }

  .service-page__content {
    padding-bottom: 60px;
    padding-top: 59px;
  }

  .service-page__content h2,
  .service-page__content h3,
  .service-page__content h4,
  .service-page__content h5,
  .service-page__content h6 {
    font-size: 26px;
    margin-bottom: 32px;
    margin-top: 59px;
  }

  .service-page__content h1 {
    margin-bottom: 61px;
  }

  .footer {
    padding-bottom: 59px;
    padding-top: 80px;
  }

  .footer__blockForm {
    margin-top: 33px;
  }

  .footer__blockForm .text-above-form {
    margin: 0 0 21px !important;
  }

  .footer__bottom {
    justify-content: center;
    padding-top: 31px;
  }

  .footer__copyright {
    text-align: center;
  }

  .footer__menu {
    display: none;
  }

  .footer__element {
    display: none;
  }
}

@media screen and (max-width: 480.98px) {
  .block-search-form .tabs .tabContent.content-active {
    padding-bottom: 11px;
  }

  .block-search-form .advanced-search-button {
    display: table;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
  }

  .search-form__content.all-fields .search-form__column {
    width: 100%;
  }

  .search-form__content.all-fields .wrap-field.price span {
    width: 2px;
  }

  .search-form__column.id {
    margin-right: 8px !important;
  }

  .search-form__column {
    width: 100% !important;
  }

  .search-form .wrap-field.price input:nth-child(1), .search-form .wrap-field.price input:nth-child(3) {
    width: 50%;
  }

  .search-form__submit {
    flex: 0 0 calc(100% - 17px);
    margin: 23px 8.5px 0;
    width: calc(100% - 17px);
  }

  .apartments {
    margin-top: 58px;
  }

  .apartments.grid {
    margin-left: 0;
    margin-right: 0;
    padding-bottom: 106px;
  }

  .apartments.grid .apartment {
    margin: 0 0 32px;
    width: 100%;
  }

  .apartments.grid .apartment .object__blockImage {
    padding-bottom: 56.2%;
  }

  .apartments.list .apartment {
    margin-bottom: 32px;
  }

  .apartments.list .apartment .object__blockImage {
    padding-bottom: 56.2%;
  }

  .type-of-tiles {
    display: none;
  }

  .team__item {
    margin-bottom: 27px;
  }

  .employee__blockPhoto {
    padding-bottom: 82%;
  }

  .employee__blockPhoto img {
    -o-object-position: top;
       object-position: top;
  }

  .section-our-team .gallery__wrap-slider.only-tablet-and-pc {
    display: none;
  }
}

@media screen and (max-width: 380.98px) {
  .header__blockLogo {
    min-width: 150px;
    max-width: 150px;
    margin-right: 10px;
  }
}

@media screen and (max-width: 360.98px) {
  .footer__email {
    word-break: break-all;
  }
}

@media screen and (min-width: 768px) and (max-width: 1320px) {
  .apartament__wrap-slider {
    height: calc(482px + 318 * ((100vw - 320px) / 1000));
  }
}

@media (any-hover: hover) {
  .breadcrumbs__link {
    transition: 0.3s ease 0s;
  }

  .breadcrumbs__link:hover {
    color: #00B6DA;
  }
}

@media (max-height: 575px) {
  .header__wrap-social-and-phone {
    height: 52px;
    padding-bottom: 0;
  }

  .header__social {
    margin-bottom: -8px;
  }

  .header__phone {
    font-size: 14px;
  }

  .header__blockForm {
    bottom: 0;
    padding-bottom: 25px;
    padding-top: 20px;
  }

  .header .select__current,
  .language__current {
    font-size: 12px;
  }
}