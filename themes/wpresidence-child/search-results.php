<?php
/**
 * Template Name: Search Results
 * 
 * Custom template for property search results
 */

get_header(); ?>

<main class="main">
    <div class="n_container">
        <div class="page__head">
            <h1 class="page__title"><?= __('Найдите лучшую квартиру', 'wpresidence-child'); ?></h1>
            <p class="page__desc"><?= __('Мы знаем все о недвижимости в Алании и на Северном Кипре, потому что сами здесь живем, воспитываем детей и строим будущее.', 'wpresidence-child'); ?></p>
        </div>
    </div>

    <?php get_template_part('inc/searchform'); ?>

    <div class="n_container">
        <?php
        // DEBUG: Вивід аргументів WP_Query та країни
        global $search_query;
        if (function_exists('create_search_query')) {
            // Спробуємо отримати $args з глобальної області (якщо потрібно, можна зберігати $args у глобальну змінну)
            //global $wp_query_args_debug, $wp_query_tax_debug, $wp_query_country_debug;
            if (isset($wp_query_args_debug)) {
                /*echo '<pre style="background:#fffbe6;border:1px solid #e6c200;padding:10px;margin-bottom:20px;"><b>DEBUG WP_QUERY ARGS:</b>\n';
                print_r($wp_query_args_debug);
                echo '</pre>';*/
            }
            if (isset($wp_query_tax_debug)) {
                /*echo '<pre style="background:#fffbe6;border:1px solid #e6c200;padding:10px;margin-bottom:20px;"><b>DEBUG TAX_QUERY (country):</b>\n';
                print_r($wp_query_tax_debug);
                echo '</pre>';*/
            }
        }
        ?>
        <div class="apartments__head">
            <div class="left">
                <p><?= __('Найдено', 'wpresidence-child'); ?> <span><?php
                    global $search_query;
                    if (isset($search_query) && $search_query) {
                        echo $search_query->found_posts;
                    } else {
                        echo '0';
                    }
                ?></span> <?= __('объектов', 'wpresidence-child'); ?></p>
            </div>
	        <?php get_template_part('inc/sort-by'); ?>
        </div>

        <?php 
        global $search_query;
        if (isset($search_query) && $search_query && $search_query->have_posts()) : ?>
            <div class="apartments grid">
                <?php while ($search_query->have_posts()) : $search_query->the_post(); ?>
                    <?php
                    // Вивід усіх термінів для поточного поста
                    /*$taxonomies = get_object_taxonomies(get_post_type(), 'names');
                    foreach ($taxonomies as $taxonomy) {
                        $terms = get_the_terms(get_the_ID(), $taxonomy);
                        if ($terms && !is_wp_error($terms)) {
                            echo '<pre style="background:#e6f7ff;border:1px solid #1890ff;padding:5px;margin-bottom:5px;">';
                            echo '<b>' . esc_html($taxonomy) . ':</b> ';
                            print_r($terms);
                            echo '</pre>';
                        }
                    }*/
                    ?>
                    <!-- DEBUG: Вивід площі -->
	                <?php //print_r( get_post_meta(get_the_ID(), 'property_price', true)); ?>
                    <div class="apartment">
                        <?php get_template_part('templates/property_unit_list'); ?>
                    </div>
                <?php endwhile; ?>
            </div>

            <?php
            // Пагінація для нашого запиту
            $big = *********;
            $pagination = paginate_links(array(
                'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                'format' => '?paged=%#%',
                'current' => max(1, get_query_var('paged')),
                'total' => $search_query->max_num_pages,
                'mid_size' => 2,
                'prev_text' => __('Назад', 'wpresidence-child'),
                'next_text' => __('Вперед', 'wpresidence-child'),
            ));
            
            if ($pagination) {
                echo '<div class="pagination">' . $pagination . '</div>';
            }
            ?>

        <?php else : ?>
            <div class="no-results">
                <h2><?= __('Результатов не найдено', 'wpresidence-child'); ?></h2>
                <p><?= __('Попробуйте изменить параметры поиска', 'wpresidence-child'); ?></p>
            </div>
        <?php endif; ?>
        
        <?php 
        // Reset post data
        if (isset($search_query)) {
            wp_reset_postdata();
        }
        ?>
    </div>

	<?php get_template_part('inc/top-search-result'); ?>

	<?php get_template_part('inc/contact-us'); ?>
</main>

<?php get_footer(); ?> 