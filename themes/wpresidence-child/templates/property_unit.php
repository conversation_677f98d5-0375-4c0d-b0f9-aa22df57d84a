<?php
/**
 * Property card template (custom markup)
 *
 * @package WordPress
 * @subpackage wpresidence-child
 */

if ( ! function_exists( 'get_the_term_list' ) ) {
    // Try to load WordPress if not loaded (for direct access)
    $wp_load_path = dirname( __FILE__, 5 ) . '/wp-load.php';
    if ( file_exists( $wp_load_path ) ) {
        require_once $wp_load_path;
    }
}

global $align;
global $wpestate_options;
global $is_shortcode;
global $row_number_col;
global $post;

if(!isset( $wpestate_property_unit_slider)){
    $wpestate_property_unit_slider='';
}

$conten_class="";
if(isset($wpestate_options['content_class'])) $conten_class=$wpestate_options['content_class'];

$col_data       =   wpestate_return_unit_class($wpestate_no_listins_per_row,$conten_class,$align,$is_shortcode,$row_number_col,$wpestate_property_unit_slider);
$title          =   get_the_title();
$link           =   esc_url( get_permalink() );

$main_image     =   wpestate_return_property_card_main_image($post->ID, 'listing_full_slider');

$wp_estate_use_composer_details = wpresidence_get_option('wp_estate_use_composer_details', '');

$city = strip_tags(get_the_term_list($post->ID, 'property_city', '', ', ', ''));
$square = get_post_meta($post->ID, 'd0bfd0bbd0bed189d0b0d0b4d18c', true);
$terms = get_the_terms(get_the_ID(), 'room'); // Терміни таксономії room
if ($terms && !is_wp_error($terms)) {
	$room_names = wp_list_pluck($terms, 'name');
	$rooms = implode(', ', $room_names);
} else {
	$rooms = '';
}
$to_sea = get_post_meta($post->ID, 'd0b4d0be-d0bcd0bed180d18f', true);
$price = get_post_meta($post->ID, 'd186d0b5d0bdd0b0', true);
$currency = get_post_meta($post->ID, 'property_currency', true);
$object_id = $post->ID;
?>

<div class="<?php echo esc_html($col_data['col_class']);?> listing_wrapper "
    data-org="<?php echo esc_attr($col_data['col_org']);?>"
    data-main-modal="<?php echo esc_attr($main_image); ?>"
    data-modal-title="<?php echo esc_attr($title);?>"
    data-modal-link="<?php echo esc_attr($link);?>"
    data-listid="<?php echo intval($post->ID);?>" >

    <div class="newest-objects__object swiper-slide">
        <div class="object__blockImage">
            <div class="tags">
                <?php
                $tags = get_the_terms(get_the_ID(), 'property_action_category');
                if ($tags && !is_wp_error($tags)) {
                    foreach ($tags as $tag) {
                        $class = 'tag';
                        if ($tag->name === 'Продажа') {
                            $class .= ' vivid-orange';
                        } elseif ($tag->name === 'Аренда') {
                            $class .= ' vivid-red';
                        }
                        echo '<span class="' . esc_attr($class) . '">' . esc_html($tag->name) . '</span>';
                    }
                }
                ?>
            </div>
            <a href="<?php the_permalink(); ?>">
                <?php if (has_post_thumbnail()) : ?>
                    <img loading="lazy" src="<?php the_post_thumbnail_url('large'); ?>" alt="<?php the_title_attribute(); ?>">
                <?php else : ?>
                    <img loading="lazy" src="<?php echo esc_url(get_stylesheet_directory_uri()); ?>/html/images/photo-object-default.png" alt="Default Image">
                <?php endif; ?>
            </a>
            <p class="object__id">ID: <?php echo esc_html($object_id); ?></p>
        </div>
        <h3 class="object__name"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
        <ul class="object__list">
            <li class="location-icon"><?= __('Расположение', 'wpresidence-child'); ?>: <strong><?php echo esc_html($city ? $city : '-'); ?></strong></li>
            <li class="square-icon"><?= __('Площадь', 'wpresidence-child'); ?>: <strong><?php echo esc_html($square ? $square : '-'); ?></strong></li>
            <li class="rooms-icon"><?= __('Комнат', 'wpresidence-child'); ?>: <strong><?php echo esc_html($rooms ? $rooms : '-'); ?></strong></li>
            <li class="to-sea-icon"><?= __('До моря', 'wpresidence-child'); ?>: <strong><?php echo esc_html($to_sea ? $to_sea : '-'); ?></strong></li>
        </ul>
        <a href="<?php the_permalink(); ?>" class="object__button"><span class="currency"><?php echo esc_html($currency ? $currency : '€'); ?></span> <?php echo esc_html($price ? $price : '-'); ?></a>
    </div>
</div>
