<?php 
global  $wp_estate_global_page_template; 
global  $wp_estate_local_page_template;
global  $wpestate_options;
global $propid;

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$show_print    = esc_html( wpresidence_get_option( 'wp_estate_show_hide_print_button', '' ) );
$show_favorite = esc_html( wpresidence_get_option( 'wp_estate_show_hide_fav_button', '' ) );
$show_share    = esc_html( wpresidence_get_option( 'wp_estate_show_hide_share_button', '' ) );
$show_address  = esc_html( wpresidence_get_option( 'wp_estate_show_hide_address_details', '' ) );

// Check if required WordPress functions exist
if (
    !function_exists('the_title') ||
    !function_exists('get_post_meta') ||
    !function_exists('get_the_ID') ||
    !function_exists('esc_html')
) {
    echo '<p>WordPress environment not loaded properly.</p>';
    return;
}

 $page_to_load='';
            
if ($wp_estate_local_page_template!=0){
    $page_to=$wp_estate_local_page_template;
}else{
   $page_to= $wp_estate_global_page_template;
}

$team = get_field('team', 'option'); // img, name, position, text, phone, fb, ins, ws, vb
            
?>

    <main class="main">

        <div class="n_container">

	        <?php get_template_part('templates/breadcrumbs'); ?>

            <div class="apartament__page-head">
                <div class="left">
                    <h1><?php the_title(); ?></h1>

                    <div class="prop_social">
		                <?php
		                if($show_share =='yes'){

			                print wpestate_share_unit_desing($post->ID,1);?>
                            <div class="title_share share_list single_property_action"  data-original-title="<?php esc_attr_e('share this page','wpresidence');?>" >
                                <i class="fas fa-share-alt"></i><?php esc_html_e('Share','wpresidence'); ?>
                            </div>
		                <?php  }

		                if($show_favorite=='yes'){
			                include(locate_template('templates/listing_templates/property-page-templates/favorite_under_title.php'));
		                }

		                if($show_print=='yes'){ ?>
                            <div id="print_page" class="title_share single_property_action"   data-propid="<?php echo intval($post->ID);?>" data-original-title="<?php esc_attr_e('print page','wpresidence');?>" >
                                <i class="fas fa-print"></i><?php esc_html_e('Print','wpresidence'); ?>
                            </div>
		                <?php } ?>


                    </div>

                </div>
                <div class="right">
                    <?php
                    $price = get_post_meta(get_the_ID(), 'd186d0b5d0bdd0b0', true); // замініть на ваш ключ ціни
                    $currency = get_post_meta(get_the_ID(), 'property_currency', true);
                    ?>
                    <p class="price"><span class="currency"><?php echo esc_html($currency ? $currency : '€'); ?></span> <?php echo esc_html($price ? $price : '-'); ?></p>
                    <p class="id"><?php echo esc_html(get_the_ID()); ?></p>
                </div>
            </div>

            <div class="apartament__wrap-slider">
                <div id="apartament-big-slider" class="apartament__big-slider swiper">
                    <div class="swiper-wrapper">
                        <?php
                        $photo_ids = array();
                        $photo_full_urls = array();
                        if (has_post_thumbnail()) {
                            $thumb_id = get_post_thumbnail_id();
                            $photo_ids[] = $thumb_id;
                            $photo_full_urls[] = wp_get_attachment_image_url($thumb_id, 'full');
                        }
                        $gallery = wpestate_return_property_images($propid);
                        if (!empty($gallery) && is_array($gallery)) {
                            foreach ($gallery as $img) {
                                if (is_object($img) && isset($img->ID)) {
                                    if (!in_array($img->ID, $photo_ids)) {
                                        $photo_ids[] = $img->ID;
                                        $photo_full_urls[] = wp_get_attachment_image_url($img->ID, 'full');
                                    }
                                }
                            }
                        }

                        $property_action = get_the_terms($post->ID, 'property_action_category');
                        $property_category = get_the_terms($post->ID, 'property_category');

                        // Динамічний вивід тегів категорій і дій
                        $tags_html = '<div class="tags">';
                        if ($property_action && !is_wp_error($property_action)) {
                            foreach ($property_action as $action) {
                                $action_link = get_term_link($action);
                                if (!is_wp_error($action_link)) {
                                    $tags_html .= '<a class="tag vivid-orange" href="' . esc_url($action_link) . '" rel="tag">' . esc_html($action->name) . '</a>';
                                }
                            }
                        }
                        if ($property_category && !is_wp_error($property_category)) {
                            foreach ($property_category as $cat) {
                                $cat_link = get_term_link($cat);
                                if (!is_wp_error($cat_link)) {
                                    $tags_html .= '<a class="tag vivid-red" href="' . esc_url($cat_link) . '" rel="tag">' . esc_html($cat->name) . '</a>';
                                }
                            }
                        }
                        $tags_html .= '</div>';

                        // Динамічний вивід посилань на архіви термінів
                        echo '<div class="single_property_labels">';
                        if ($property_action && !is_wp_error($property_action)) {
                            foreach ($property_action as $action) {
                                $action_link = get_term_link($action);
                                if (!is_wp_error($action_link)) {
                                    echo '<div class="property_title_label"><a href="' . esc_url($action_link) . '" rel="tag">' . esc_html($action->name) . '</a></div>';
                                }
                            }
                        }
                        if ($property_category && !is_wp_error($property_category)) {
                            $cat_links = array();
                            foreach ($property_category as $cat) {
                                $cat_link = get_term_link($cat);
                                if (!is_wp_error($cat_link)) {
                                    $cat_links[] = '<a href="' . esc_url($cat_link) . '" rel="tag">' . esc_html($cat->name) . '</a>';
                                }
                            }
                            if (!empty($cat_links)) {
                                echo '<div class="property_title_label actioncat">' . implode(', ', $cat_links) . '</div>';
                            }
                        }
                        echo '</div>';

                        foreach ($photo_ids as $i => $img_id) {
                            $img_url = wp_get_attachment_image_url($img_id, 'large');
                            $img_full = $photo_full_urls[$i];
	                        if ($img_url) {
                                echo '<div class="swiper-slide">';
	                            echo $tags_html;
                                echo '<img loading="lazy" src="' . esc_url($img_url) . '" alt="">';
                                echo '<div class="swiper-lazy-preloader"></div>';
                                echo '<a href="' . esc_url($img_full) . '" class="apartament-big-slider-show-all-photos js-show-all-photos"><span>'. __('Смотреть все фото','wpresidence-child') .'</span></a>';
                                echo '</div>';
                            }
                        }
                        ?>
                    </div>
                    <div class="apartament-wrap-buttons-swiper">
                        <div id="apartament-button-prev" class="swiper-button-prev apartament-button-swiper">
                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13 5L1 5M1 5L5.5 9.5M1 5L5.5 0.5" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div id="apartament-button-next" class="swiper-button-next apartament-button-swiper">
                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 5L13 5M13 5L8.5 9.5M13 5L8.5 0.5" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div id="apartament-gallery-slider" class="apartament__gallery-slider swiper">
                    <div class="swiper-wrapper">
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="swiper-slide">
                                <img loading="lazy" src="<?php the_post_thumbnail_url('large'); ?>" alt="<?php the_title_attribute(); ?>">
                                <div class="swiper-lazy-preloader"></div>
                            </div>
                        <?php endif; ?>
                        <?php
                        $gallery = wpestate_return_property_images($propid);
                        if (!empty($gallery) && is_array($gallery)) {
                            foreach ($gallery as $img) {
                                if (is_object($img) && isset($img->ID)) {
                                    $img_url = wp_get_attachment_image_url($img->ID, 'large');
                                    if ($img_url) {
                                        echo '<div class="swiper-slide"><img loading="lazy" src="' . esc_url($img_url) . '" alt=""><div class="swiper-lazy-preloader"></div></div>';
                                    }
                                }
                            }
                        }
                        ?>
                    </div>
                </div>
            </div>

            <div class="apartament__items">
                <div class="apartament__item">
                    <div class="apartament__itemBlock">
                        <h2><?= __('Краткая информация', 'wpresidence'); ?></h2>
                        <ul class="apartament__shortInfoList">
                            <li class="id">
                                <span class="js-name">ID:</span>
                                <strong><?php echo esc_html(get_the_ID()); ?></strong>
                            </li>
                            <li class="location">
                                <span class="js-name"><?= __('Расположение', 'wpresidence-child'); ?>:</span>
                                <?php
                                $city_terms = get_the_terms(get_the_ID(), 'property_city');
                                if ($city_terms && !is_wp_error($city_terms)) {
	                                $city     = $city_terms[0]->name;
	                                $maps_url = 'https://www.google.com/maps/search/?api=1&query=' . urlencode( $city );
                                }
	                            ?>
                                <strong><a href="<?php echo esc_url($maps_url); ?>" target="_blank"><?php echo esc_html($city); ?></a></strong>
                            </li>
                            <li class="property">
                                <span class="js-name"><?= __('Категория', 'wpresidence-child'); ?>:</span>
                                <strong><?php echo esc_html(strip_tags(get_the_term_list(get_the_ID(), 'property_category', '', ', ', ''))); ?></strong>
                            </li>
                            <li class="to-sea">
                                <span class="js-name"><?= __('До моря', 'wpresidence-child'); ?>:</span>
                                <strong><?php echo esc_html(get_post_meta(get_the_ID(), 'd0b4d0be-d0bcd0bed180d18f', true)); ?></strong>
                            </li>

                                <?php
                                $has_109 = false;
                                if ($property_action && !is_wp_error($property_action)) {
                                    foreach ($property_action as $action) {
                                        if ((int)$action->term_id === 109) {
                                            $has_109 = true;
                                            break;
                                        }
                                    }
                                }
                                if (isset($has_109) && $has_109) {
                                    // Виводимо локацію з посиланням на Google Maps по координатах
                                    $latitude = get_post_meta(get_the_ID(), 'property_latitude', true);
                                    $longitude = get_post_meta(get_the_ID(), 'property_longitude', true);
                                    
                                    if (!empty($latitude) && !empty($longitude)) {
                                        $maps_url = 'https://www.google.com/maps/search/?api=1&query=' . urlencode($latitude . ',' . $longitude);
                                        ?>
                                    <li class="location">
                                        <span style="width: 123px;"><?= __('Локация', 'wpresidence-child'); ?>:</span>
                                        <strong><a href="<?php echo esc_url($maps_url); ?>" target="_blank"><?php echo __('Смотреть на карте', 'wpresidence-сhild'); ?></a></strong>
                                    </li>
                                    <?php } else {
                                        // Якщо координат немає, виводимо місто
                                        $city_terms = get_the_terms(get_the_ID(), 'property_city');
                                        if ($city_terms && !is_wp_error($city_terms)) {
                                            $city = $city_terms[0]->name;
                                            $maps_url = 'https://www.google.com/maps/search/?api=1&query=' . urlencode($city);
                                            ?>
                                            <li class="location" style="width: 123px;">
                                            <span><?= __('Локация', 'wpresidence-child'); ?>:</span>
                                            <strong><a href="<?php echo esc_url($maps_url); ?>" target="_blank"><?php echo __('Смотреть на карте', 'wpresidence-сhild'); ?></a></strong>
                                            </li>
                                        <?php } else { ?>
                                            <li class="construction">
                                            <span style="width: 123px;"><?= __('Локация', 'wpresidence-child'); ?>:</span>
                                            <strong>-</strong>
                                            </li>
                                        <?php }
                                    }
                                } else { ?>
                                    <li class="construction">
                                    <span class="js-name"><?= __('Год строительства', 'wpresidence-child'); ?>:</span>
                                    <strong><?php echo esc_html(get_post_meta(get_the_ID(), 'd0b3d0bed0b4-d181d182d180d0bed', true)); ?></strong>
                                    </li>
                                <?php } ?>
                            <li class="layout">
                                <span><?= __('Комнат', 'wpresidence-child'); ?>:</span>
                                <strong>
                                <?php
$terms = get_the_terms(get_the_ID(), 'room');
if ($terms && !is_wp_error($terms)) {
    $room_names = wp_list_pluck($terms, 'name');
    echo esc_html(implode(', ', $room_names));
}
?></strong>
                            </li>
                            <li class="square">
                                <span><?= __('Площадь', 'wpresidence-child'); ?>:</span>
                                <strong><?php echo esc_html(get_post_meta(get_the_ID(), 'd0bfd0bbd0bed189d0b0d0b4d18c', true)); ?></strong>
                            </li>
                            <li class="furniture">
                                <span><?= __('Мебель', 'wpresidence-child'); ?>:</span>
                                <strong><?php echo esc_html(get_post_meta(get_the_ID(), 'd0bcd0b5d0b1d0b5d0bbd18c', true)); ?></strong>
                            </li>
                            <li class="floor">
                                <span><?= __('Этаж', 'wpresidence-child'); ?>:</span>
                                <strong><?php echo esc_html(get_post_meta(get_the_ID(), 'd18dd182d0b0d0b6', true)); ?></strong>
                            </li>
                            <li class="offer-from">
                                <?php
                                // Аренда ID 109
                                $has_109 = false;
                                if ($property_action && !is_wp_error($property_action)) {
                                    foreach ($property_action as $action) {
                                        if ((int)$action->term_id === 109) {
                                            $has_109 = true;
                                            break;
                                        }
                                    }
                                }
                                if ($has_109) {
                                    // 'Количество человек'
                                    $kolichestvo = get_post_meta(get_the_ID(), 'd0bad0bed0bbd0b8d187d0b5d181d1', true);
                                    ?>
                                    <span><?= __('Количество человек', 'wpresidence-child'); ?>:</span>
                                    <strong><?php echo esc_html($kolichestvo); ?></strong>
                                <?php } else { ?>
                                    <span><?= __('Предложение', 'wpresidence-child'); ?></span>
                                    <strong><?php echo get_the_title(get_post_meta(get_the_ID(), 'property_agent', true)); ?></strong>
                                <?php } ?>
                            </li>
                        </ul>
                    </div>
                    <div class="apartament__itemBlock">
                        <h2><?= __('Инфраструктура', 'wpresidence'); ?></h2>
                        <ul class="apartament__socialList">
                            <?php
                            $features = get_the_terms(get_the_ID(), 'property_features');
                            if ($features && !is_wp_error($features)) {
                                foreach ($features as $feature) {
                                    echo '<li>' . esc_html($feature->name) . '</li>';
                                }
                            } else {
                                echo '<li>' . esc_html__("No features specified.", "wpresidence-child") . '</li>';
                            }
                            ?>
                        </ul>
                    </div>
                    <div class="apartament__itemBlock">
                        <h2><?= __('Описание', 'wpresidence'); ?></h2>
                        <div class="text">
                            <div itemprop="description">
                                <div class="item-info-box">
                                    <p class="item-info-title"><?= __('Что вы получаете', 'wpresidence-child'); ?></p>
                                    <p class="item-info-text"><?php the_content(); ?></p>
                                </div>
                                <?php
                                $acf_groups = [
                                    'районпляж',
                                    'что_в_комплексе',
                                    'внутри',
                                    'кому_может_быть_интерсно',
                                ];
                                foreach ( $acf_groups as $group ) {
                                    $data = get_field( $group );
                                    if ( ! empty( $data['title'] ) && ! empty( $data['desc'] ) ) {
                                        ?>
                                        <div class="item-info-box">
                                            <p class="item-info-title"><?php echo $data['title']; ?></p>
                                            <p class="item-info-text"><?php echo $data['desc']; ?></p>
                                        </div>
                                        <?php
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="apartament__item">
                    <aside class="apartament__sidebar">
                        <div data-da="apartament__items, 0, 960" class="apartament__widget want-to-know-more">
                            <h3><?= __('Хотите <span>узнать</span> больше?', 'wpresidence-child'); ?></h3>
                            <p><?= __('Апартаменты в 200 м от моря в тихом районе Каргыджака.', 'wpresidence-child'); ?></p>
                            <a href="#elementor-action%3Aaction%3Dpopup%3Aopen%26settings%3DeyJpZCI6IjMxNDE1IiwidG9nZ2xlIjpmYWxzZX0%3D" class="apartament__button color"><?= __('Оставить запрос', 'wpresidence'); ?></a>
                            <a href="#elementor-action%3Aaction%3Dpopup%3Aopen%26settings%3DeyJpZCI6IjMxNDE1IiwidG9nZ2xlIjpmYWxzZX0%3D" class="apartament__button video popup-link"><span><?= __('Организовать показ объекта', 'wpresidence'); ?></span></a>
                            <a href="#elementor-action%3Aaction%3Dpopup%3Aopen%26settings%3DeyJpZCI6IjMxNDE1IiwidG9nZ2xlIjpmYWxzZX0%3D" class="apartament__button calendar-clock"><span><?= __('Записаться на просмотр', 'wpresidence'); ?></span></a>
                        </div>
                        <div class="apartament__widget managers">
                            <h3><?= __('Менеджеры', 'wpresidence'); ?></h3>
                            <?php if ($team && is_array($team)) : ?>
                                <div class="managers-wrap-slider">
                                    <div id="managers-slider" class="managers-slider swiper-conteiner">
                                        <div class="swiper-wrapper">
                                            <?php foreach ($team as $manager) :
                                                $img = isset($manager['img']) ? $manager['img'] : '';
                                                $name = isset($manager['name']) ? $manager['name'] : '';
                                                $position = isset($manager['position']) ? $manager['position'] : '';
                                                $email = isset($manager['email']) ? $manager['email'] : '';
                                                $phone = isset($manager['phone']) ? $manager['phone'] : '';
                                                $fb = isset($manager['fb']) ? $manager['fb'] : '';
                                                $ins = isset($manager['ins']) ? $manager['ins'] : '';
                                                $ws = isset($manager['ws']) ? $manager['ws'] : '';
                                                $vb = isset($manager['vb']) ? $manager['vb'] : '';
                                                if (!empty($name)) : ?>
                                                    <div class="swiper-slide">
                                                        <div class="managers-slider__image">
                                                            <?php if (!empty($img)) { ?>
                                                                <img class="swiper-lazy" src="<?php echo esc_url($img); ?>" alt="<?php echo esc_attr($name); ?>">
                                                            <?php } else { ?>
                                                                <img class="swiper-lazy" src="<?php echo esc_url(get_stylesheet_directory_uri()) ?>/html/images/manager.jpg" alt="<?php echo esc_attr($name); ?>">
                                                            <?php } ?>
                                                            <div class="swiper-lazy-preloader"></div>
                                                        </div>
                                                        <h4><?php echo esc_html($name); ?></h4>
                                                        <?php if (!empty($position)) : ?>
                                                            <p><?php echo esc_html($position); ?></p>
                                                        <?php endif; ?>
                                                        <?php if (!empty($email)) : ?>
                                                            <a href="mailto:<?php echo esc_attr($email); ?>"><?php echo esc_html($email); ?></a>
                                                        <?php endif; ?>
                                                        <?php if (!empty($phone)) : ?>
                                                            <a href="tel:<?php echo esc_attr($phone); ?>"><?php echo esc_html($phone); ?></a>
                                                        <?php endif; ?>
                                                        <ul class="managers-slider__icons">
                                                            <li>
                                                                <a href="<?php echo !empty($fb) ? esc_url($fb) : '#'; ?>" target="_blank">
                                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M14.6666 8.00065C14.6666 4.32065 11.6799 1.33398 7.99992 1.33398C4.31992 1.33398 1.33325 4.32065 1.33325 8.00065C1.33325 11.6827 4.31992 14.6673 7.99992 14.6673C11.6799 14.6673 14.6666 11.6827 14.6666 8.00065ZM5.72866 4.86732L5.59533 4.87265C5.50912 4.87859 5.42489 4.90123 5.34733 4.93932C5.27504 4.98033 5.20904 5.03152 5.15133 5.09132C5.07133 5.16665 5.02599 5.23198 4.97733 5.29532C4.73074 5.61592 4.59798 6.00953 4.59999 6.41398C4.60133 6.74065 4.68666 7.05865 4.81999 7.35598C5.09266 7.95732 5.54133 8.59398 6.13333 9.18398C6.27599 9.32598 6.416 9.46865 6.56666 9.60132C7.30228 10.2489 8.17885 10.716 9.12666 10.9653L9.50533 11.0233C9.62866 11.03 9.752 11.0207 9.876 11.0147C10.0701 11.0044 10.2597 10.9519 10.4313 10.8607C10.5186 10.8155 10.6038 10.7666 10.6867 10.714C10.6867 10.714 10.7149 10.6949 10.77 10.654C10.86 10.5873 10.9153 10.54 10.99 10.462C11.046 10.4042 11.0927 10.3371 11.13 10.2607C11.182 10.152 11.234 9.94465 11.2553 9.77198C11.2713 9.63998 11.2667 9.56798 11.2647 9.52332C11.262 9.45198 11.2027 9.37799 11.138 9.34665L10.75 9.17265C10.75 9.17265 10.17 8.91998 9.81533 8.75865C9.7782 8.74249 9.73844 8.73323 9.698 8.73132C9.65238 8.72655 9.60627 8.73164 9.56279 8.74624C9.51931 8.76085 9.47948 8.78464 9.446 8.81598C9.44266 8.81465 9.39799 8.85265 8.91599 9.43665C8.88833 9.47383 8.85022 9.50192 8.80653 9.51735C8.76284 9.53279 8.71554 9.53487 8.67066 9.52332C8.62721 9.51174 8.58466 9.49703 8.54333 9.47932C8.46066 9.44465 8.43199 9.43132 8.37533 9.40732C7.99258 9.24059 7.63829 9.01497 7.32533 8.73865C7.24133 8.66532 7.16333 8.58532 7.08333 8.50798C6.82107 8.25679 6.5925 7.97264 6.40333 7.66265L6.36399 7.59932C6.33617 7.55652 6.31335 7.51066 6.29599 7.46265C6.27066 7.36465 6.33666 7.28598 6.33666 7.28598C6.33666 7.28598 6.49866 7.10865 6.57399 7.01265C6.64733 6.91932 6.70933 6.82865 6.74933 6.76398C6.82799 6.63732 6.85266 6.50732 6.81133 6.40665C6.62466 5.95065 6.43177 5.4971 6.23266 5.04598C6.19333 4.95665 6.07666 4.89265 5.97066 4.87998C5.93466 4.87554 5.89866 4.87198 5.86266 4.86932C5.77315 4.86418 5.68339 4.86507 5.59399 4.87198L5.72866 4.86732Z" fill="#00B6DA"/>
                                                        </svg>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="<?php echo !empty($ins) ? esc_url($ins) : '#'; ?>" target="_blank">
                                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M14.6666 8.00065C14.6666 4.32065 11.6799 1.33398 7.99992 1.33398C4.31992 1.33398 1.33325 4.32065 1.33325 8.00065C1.33325 11.6827 4.31992 14.6673 7.99992 14.6673C11.6799 14.6673 14.6666 11.6827 14.6666 8.00065ZM5.72866 4.86732L5.59533 4.87265C5.50912 4.87859 5.42489 4.90123 5.34733 4.93932C5.27504 4.98033 5.20904 5.03152 5.15133 5.09132C5.07133 5.16665 5.02599 5.23198 4.97733 5.29532C4.73074 5.61592 4.59798 6.00953 4.59999 6.41398C4.60133 6.74065 4.68666 7.05865 4.81999 7.35598C5.09266 7.95732 5.54133 8.59398 6.13333 9.18398C6.27599 9.32598 6.416 9.46865 6.56666 9.60132C7.30228 10.2489 8.17885 10.716 9.12666 10.9653L9.50533 11.0233C9.62866 11.03 9.752 11.0207 9.876 11.0147C10.0701 11.0044 10.2597 10.9519 10.4313 10.8607C10.5186 10.8155 10.6038 10.7666 10.6867 10.714C10.6867 10.714 10.7149 10.6949 10.77 10.654C10.86 10.5873 10.9153 10.54 10.99 10.462C11.046 10.4042 11.0927 10.3371 11.13 10.2607C11.182 10.152 11.234 9.94465 11.2553 9.77198C11.2713 9.63998 11.2667 9.56798 11.2647 9.52332C11.262 9.45198 11.2027 9.37799 11.138 9.34665L10.75 9.17265C10.75 9.17265 10.17 8.91998 9.81533 8.75865C9.7782 8.74249 9.73844 8.73323 9.698 8.73132C9.65238 8.72655 9.60627 8.73164 9.56279 8.74624C9.51931 8.76085 9.47948 8.78464 9.446 8.81598C9.44266 8.81465 9.39799 8.85265 8.91599 9.43665C8.88833 9.47383 8.85022 9.50192 8.80653 9.51735C8.76284 9.53279 8.71554 9.53487 8.67066 9.52332C8.62721 9.51174 8.58466 9.49703 8.54333 9.47932C8.46066 9.44465 8.43199 9.43132 8.37533 9.40732C7.99258 9.24059 7.63829 9.01497 7.32533 8.73865C7.24133 8.66532 7.16333 8.58532 7.08333 8.50798C6.82107 8.25679 6.5925 7.97264 6.40333 7.66265L6.36399 7.59932C6.33617 7.55652 6.31335 7.51066 6.29599 7.46265C6.27066 7.36465 6.33666 7.28598 6.33666 7.28598C6.33666 7.28598 6.49866 7.10865 6.57399 7.01265C6.64733 6.91932 6.70933 6.82865 6.74933 6.76398C6.82799 6.63732 6.85266 6.50732 6.81133 6.40665C6.62466 5.95065 6.43177 5.4971 6.23266 5.04598C6.19333 4.95665 6.07666 4.89265 5.97066 4.87998C5.93466 4.87554 5.89866 4.87198 5.86266 4.86932C5.77315 4.86418 5.68339 4.86507 5.59399 4.87198L5.72866 4.86732Z" fill="#00B6DA"/>
                                                        </svg>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="<?php echo !empty($ws) ? esc_url($ws) : '#'; ?>" target="_blank">
                                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M14.6666 8.00065C14.6666 4.32065 11.6799 1.33398 7.99992 1.33398C4.31992 1.33398 1.33325 4.32065 1.33325 8.00065C1.33325 11.6827 4.31992 14.6673 7.99992 14.6673C11.6799 14.6673 14.6666 11.6827 14.6666 8.00065ZM5.72866 4.86732L5.59533 4.87265C5.50912 4.87859 5.42489 4.90123 5.34733 4.93932C5.27504 4.98033 5.20904 5.03152 5.15133 5.09132C5.07133 5.16665 5.02599 5.23198 4.97733 5.29532C4.73074 5.61592 4.59798 6.00953 4.59999 6.41398C4.60133 6.74065 4.68666 7.05865 4.81999 7.35598C5.09266 7.95732 5.54133 8.59398 6.13333 9.18398C6.27599 9.32598 6.416 9.46865 6.56666 9.60132C7.30228 10.2489 8.17885 10.716 9.12666 10.9653L9.50533 11.0233C9.62866 11.03 9.752 11.0207 9.876 11.0147C10.0701 11.0044 10.2597 10.9519 10.4313 10.8607C10.5186 10.8155 10.6038 10.7666 10.6867 10.714C10.6867 10.714 10.7149 10.6949 10.77 10.654C10.86 10.5873 10.9153 10.54 10.99 10.462C11.046 10.4042 11.0927 10.3371 11.13 10.2607C11.182 10.152 11.234 9.94465 11.2553 9.77198C11.2713 9.63998 11.2667 9.56798 11.2647 9.52332C11.262 9.45198 11.2027 9.37799 11.138 9.34665L10.75 9.17265C10.75 9.17265 10.17 8.91998 9.81533 8.75865C9.7782 8.74249 9.73844 8.73323 9.698 8.73132C9.65238 8.72655 9.60627 8.73164 9.56279 8.74624C9.51931 8.76085 9.47948 8.78464 9.446 8.81598C9.44266 8.81465 9.39799 8.85265 8.91599 9.43665C8.88833 9.47383 8.85022 9.50192 8.80653 9.51735C8.76284 9.53279 8.71554 9.53487 8.67066 9.52332C8.62721 9.51174 8.58466 9.49703 8.54333 9.47932C8.46066 9.44465 8.43199 9.43132 8.37533 9.40732C7.99258 9.24059 7.63829 9.01497 7.32533 8.73865C7.24133 8.66532 7.16333 8.58532 7.08333 8.50798C6.82107 8.25679 6.5925 7.97264 6.40333 7.66265L6.36399 7.59932C6.33617 7.55652 6.31335 7.51066 6.29599 7.46265C6.27066 7.36465 6.33666 7.28598 6.33666 7.28598C6.33666 7.28598 6.49866 7.10865 6.57399 7.01265C6.64733 6.91932 6.70933 6.82865 6.74933 6.76398C6.82799 6.63732 6.85266 6.50732 6.81133 6.40665C6.62466 5.95065 6.43177 5.4971 6.23266 5.04598C6.19333 4.95665 6.07666 4.89265 5.97066 4.87998C5.93466 4.87554 5.89866 4.87198 5.86266 4.86932C5.77315 4.86418 5.68339 4.86507 5.59399 4.87198L5.72866 4.86732Z" fill="#00B6DA"/>
                                                        </svg>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="<?php echo !empty($vb) ? esc_url($vb) : '#'; ?>" target="_blank">
                                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1173 1.75203C9.01984 1.28318 6.84474 1.28318 4.74729 1.75203L4.52129 1.80203C3.93114 1.93358 3.38908 2.22635 2.95547 2.64772C2.52185 3.0691 2.21369 3.60256 2.06529 4.1887C1.53455 6.28383 1.53455 8.47823 2.06529 10.5734C2.20683 11.1324 2.49381 11.6439 2.89707 12.0561C3.30034 12.4683 3.80552 12.7663 4.36129 12.92L4.67129 14.7707C4.68119 14.8295 4.70666 14.8845 4.74505 14.9301C4.78344 14.9757 4.83335 15.0101 4.88957 15.0299C4.9458 15.0496 5.0063 15.0539 5.06475 15.0423C5.12321 15.0308 5.1775 15.0037 5.22196 14.964L7.04262 13.3354C8.40934 13.4179 9.78088 13.3086 11.1173 13.0107L11.344 12.9607C11.9341 12.8291 12.4762 12.5364 12.9098 12.115C13.3434 11.6936 13.6516 11.1602 13.8 10.574C14.3307 8.4789 14.3307 6.2845 13.8 4.18936C13.6515 3.60314 13.3432 3.06962 12.9095 2.64824C12.4758 2.22686 11.9336 1.93414 11.3433 1.8027L11.1173 1.75203ZM5.30996 4.1347C5.18605 4.11655 5.05968 4.1415 4.95196 4.20536H4.94262C4.69262 4.35203 4.46729 4.5367 4.27529 4.75403C4.11529 4.9387 4.02862 5.12536 4.00596 5.30536C3.99262 5.41203 4.00196 5.52003 4.03329 5.62203L4.04529 5.6287C4.22529 6.15736 4.45996 6.66603 4.74662 7.1447C5.11642 7.81692 5.57128 8.4387 6.09996 8.9947L6.11596 9.01736L6.14129 9.03603L6.15662 9.05403L6.17529 9.07003C6.73328 9.60025 7.35651 10.0573 8.02996 10.43C8.79996 10.8494 9.26729 11.0474 9.54796 11.13V11.134C9.62996 11.1594 9.70463 11.1707 9.77996 11.1707C10.0191 11.1535 10.2455 11.0563 10.4226 10.8947C10.6393 10.7027 10.8226 10.4767 10.9653 10.2254V10.2207C11.0993 9.96736 11.054 9.7287 10.8606 9.5667C10.4726 9.22698 10.0527 8.92558 9.60662 8.6667C9.30796 8.5047 9.00462 8.6027 8.88196 8.7667L8.61996 9.09736C8.48529 9.26136 8.24129 9.2387 8.24129 9.2387L8.23463 9.2427C6.41396 8.77803 5.92796 6.9347 5.92796 6.9347C5.92796 6.9347 5.90529 6.68403 6.07396 6.55603L6.40196 6.29203C6.55929 6.16403 6.66862 5.86136 6.49996 5.5627C6.24168 5.11698 5.94095 4.69725 5.60196 4.30936C5.52781 4.21828 5.42396 4.15621 5.30862 4.13403M8.38596 3.33336C8.29755 3.33336 8.21277 3.36848 8.15026 3.43099C8.08774 3.49351 8.05263 3.57829 8.05263 3.6667C8.05263 3.7551 8.08774 3.83989 8.15026 3.9024C8.21277 3.96491 8.29755 4.00003 8.38596 4.00003C9.22929 4.00003 9.92929 4.27536 10.4833 4.80336C10.768 5.09203 10.99 5.43403 11.1353 5.8087C11.2813 6.18403 11.348 6.5847 11.3306 6.98603C11.3288 7.0298 11.3356 7.07351 11.3506 7.11466C11.3657 7.1558 11.3887 7.19358 11.4184 7.22583C11.4782 7.29097 11.5616 7.32965 11.65 7.33336C11.7384 7.33708 11.8246 7.30552 11.8898 7.24563C11.9549 7.18575 11.9936 7.10244 11.9973 7.01403C12.0172 6.5204 11.9353 6.02796 11.7566 5.56736C11.5772 5.10442 11.3039 4.6836 10.954 4.33136L10.9473 4.3247C10.26 3.66803 9.38996 3.33336 8.38596 3.33336ZM8.36329 4.42936C8.27489 4.42936 8.1901 4.46448 8.12759 4.52699C8.06508 4.58951 8.02996 4.67429 8.02996 4.7627C8.02996 4.8511 8.06508 4.93589 8.12759 4.9984C8.1901 5.06091 8.27489 5.09603 8.36329 5.09603H8.37463C8.98263 5.13936 9.42529 5.34203 9.73529 5.6747C10.0533 6.01736 10.218 6.44336 10.2053 6.97003C10.2033 7.05844 10.2364 7.14403 10.2975 7.20798C10.3586 7.27193 10.4426 7.309 10.531 7.31103C10.6194 7.31306 10.705 7.27989 10.7689 7.21882C10.8329 7.15775 10.8699 7.07377 10.872 6.98536C10.888 6.29403 10.6653 5.69736 10.224 5.22136V5.22003C9.77262 4.73603 9.15329 4.48003 8.40796 4.43003L8.39663 4.4287L8.36329 4.42936ZM8.35063 5.54603C8.30602 5.54209 8.26107 5.54719 8.21847 5.56101C8.17588 5.57482 8.1365 5.59708 8.1027 5.62646C8.06889 5.65583 8.04135 5.69171 8.02172 5.73196C8.00209 5.77221 7.99078 5.81601 7.98844 5.86073C7.98611 5.90545 7.99282 5.95018 8.00816 5.99226C8.0235 6.03433 8.04716 6.07288 8.07773 6.10561C8.1083 6.13833 8.14515 6.16457 8.18608 6.18274C8.22701 6.20091 8.27118 6.21064 8.31596 6.21136C8.59462 6.22603 8.77262 6.31003 8.88462 6.4227C8.99729 6.53603 9.08129 6.71803 9.09663 7.0027C9.09746 7.04743 9.10729 7.09154 9.12553 7.13239C9.14376 7.17324 9.17004 7.21001 9.20279 7.24049C9.23554 7.27098 9.27409 7.29455 9.31615 7.30982C9.3582 7.32509 9.4029 7.33174 9.44758 7.32936C9.49226 7.32699 9.536 7.31565 9.5762 7.29601C9.6164 7.27637 9.65224 7.24885 9.68157 7.21507C9.71091 7.18129 9.73315 7.14195 9.74696 7.09939C9.76077 7.05683 9.76587 7.01193 9.76196 6.96736C9.74062 6.56736 9.61529 6.21403 9.35862 5.95403C9.10063 5.69403 8.74929 5.56736 8.35063 5.54603Z" fill="#00B6DA"/>
                                                        </svg>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                <?php endif;
                                            endforeach; ?>
                                        </div>
                                    </div>
                                    <div class="slider-buttons">
                                        <div id="managers-button-prev" class="swiper-button-prev">
                                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M15.8334 10L4.16671 10M4.16671 10L9.16671 15M4.16671 10L9.16671 5" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                        <div id="managers-button-next" class="swiper-button-next">
                                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M4.16663 10L15.8333 10M15.8333 10L10.8333 5M15.8333 10L10.8333 15" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </aside>
                </div>
            </div>

            <?php get_template_part('inc/relevant-objects'); ?>

        </div>

	    <?php get_template_part('inc/contact-us'); ?>

    </main>

    <div class="wpestate_content_wrapper_custom_template_wrapper">
<?php if (!current_user_can('administrator')) {
	$class = 'hidden';
    } ?>
<div class="hidden <?= $class; ?> row estate_property_first_row" data-prp-listingid="<?php print intval($post->ID);?>" >
    <?php //include ( locate_template('/templates/listing_templates/property-page-templates/property-page-breadcrumbs.php') );  ?>
    <div class="col-xs-12 <?php print esc_html($wpestate_options['content_class']);?> full_width_prop">
        
        <?php //get_template_part('templates/ajax_container'); ?>
        
        <?php while (have_posts()) : the_post();
        ?>

            <div class="single-content page_template_loader">
            <?php 
            
       
            $the_query = new WP_Query( 'page_id='.$page_to );
                while ( $the_query->have_posts() ) :
                        $the_query->the_post();
                      the_content();
                endwhile;
                wp_reset_postdata();

            ?></div><!-- single content-->

        <?php endwhile; // end of the loop. ?>
    </div>
  
    
<?php     
 //include get_theme_file_path('sidebar.php');?>
</div>   



<?php 

print '</div>';
print '</div>';


$mapargs = array(
        'post_type'         =>  'estate_property',
        'post_status'       =>  'publish',
        'p'                 =>  $post->ID,
        'fields'            =>    'ids');
  

$selected_pins  =  wpestate_listing_pins('blank_single',0,$mapargs,1);
wp_localize_script('googlecode_property', 'googlecode_property_vars2', 
            array('markers2'          =>  $selected_pins));

get_footer();

?>
    <script>
        Fancybox.bind(".js-show-all-photos", {
            groupAll: true, // Group all items
            on: {
                ready: (fancybox) => {
                    console.log(`fancybox #${fancybox.id} is ready!`);
                }
            }
        });
    </script>
<?php
exit();
?>