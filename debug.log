[02-Aug-2025 18:26:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-seo-pack</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Aug-2025 18:26:14 UTC] PHP Deprecated:  Constant FILTER_SANITIZE_STRING is deprecated in /var/www/html/wp-content/plugins/chaty-pro/admin/class-admin-base.php on line 62
[02-Aug-2025 18:26:14 UTC] PHP Deprecated:  Constant FILTER_SANITIZE_STRING is deprecated in /var/www/html/wp-content/plugins/chaty-pro/admin/class-admin-base.php on line 68
[02-Aug-2025 18:26:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>head-meta-data</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Aug-2025 18:26:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>wpestate-crm</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Aug-2025 18:26:14 UTC] PHP Notice:  Функция _load_textdomain_just_in_time вызвана <strong>неправильно</strong>. Загрузка перевода для домена <code>cyr2lat</code> была запущена слишком рано. Обычно это индикатор того, что какой-то код в плагине или теме запускается слишком рано. Переводы должны загружаться при выполнении действия <code>init</code> или позже. Дополнительную информацию можно найти на странице <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">&laquo;Отладка в WordPress&raquo;</a>. (Это сообщение было добавлено в версии 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Aug-2025 18:26:14 UTC] PHP Notice:  Функция _load_textdomain_just_in_time вызвана <strong>неправильно</strong>. Загрузка перевода для домена <code>insert-headers-and-footers</code> была запущена слишком рано. Обычно это индикатор того, что какой-то код в плагине или теме запускается слишком рано. Переводы должны загружаться при выполнении действия <code>init</code> или позже. Дополнительную информацию можно найти на странице <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">&laquo;Отладка в WordPress&raquo;</a>. (Это сообщение было добавлено в версии 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Aug-2025 18:26:14 UTC] PHP Notice:  Функция _load_textdomain_just_in_time вызвана <strong>неправильно</strong>. Загрузка перевода для домена <code>translatepress-multilingual</code> была запущена слишком рано. Обычно это индикатор того, что какой-то код в плагине или теме запускается слишком рано. Переводы должны загружаться при выполнении действия <code>init</code> или позже. Дополнительную информацию можно найти на странице <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">&laquo;Отладка в WordPress&raquo;</a>. (Это сообщение было добавлено в версии 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Aug-2025 18:26:14 UTC] PHP Notice:  Функция _load_textdomain_just_in_time вызвана <strong>неправильно</strong>. Загрузка перевода для домена <code>wpresidence-core</code> была запущена слишком рано. Обычно это индикатор того, что какой-то код в плагине или теме запускается слишком рано. Переводы должны загружаться при выполнении действия <code>init</code> или позже. Дополнительную информацию можно найти на странице <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">&laquo;Отладка в WordPress&raquo;</a>. (Это сообщение было добавлено в версии 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Aug-2025 18:26:14 UTC] PHP Notice:  Функция _load_textdomain_just_in_time вызвана <strong>неправильно</strong>. Загрузка перевода для домена <code>wpresidence</code> была запущена слишком рано. Обычно это индикатор того, что какой-то код в плагине или теме запускается слишком рано. Переводы должны загружаться при выполнении действия <code>init</code> или позже. Дополнительную информацию можно найти на странице <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">&laquo;Отладка в WordPress&raquo;</a>. (Это сообщение было добавлено в версии 6.7.0.) in /var/www/html/wp-includes/functions.php on line 6121
[02-Aug-2025 18:26:14 UTC] PHP Deprecated:  version_compare(): Passing null to parameter #2 ($version2) of type string is deprecated in /var/www/html/wp-content/plugins/elementor/core/experiments/manager.php on line 170
[02-Aug-2025 18:26:14 UTC] Реєстрація таксономії property_rooms
[02-Aug-2025 18:26:14 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /var/www/html/wp-content/themes/wpresidence-child/functions.php:1) in /var/www/html/wp-includes/http.php on line 514
[02-Aug-2025 18:26:14 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /var/www/html/wp-content/themes/wpresidence-child/functions.php:1) in /var/www/html/wp-includes/http.php on line 515
[02-Aug-2025 18:26:14 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /var/www/html/wp-content/themes/wpresidence-child/functions.php:1) in /var/www/html/wp-admin/admin-ajax.php on line 27
[02-Aug-2025 18:26:14 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /var/www/html/wp-content/themes/wpresidence-child/functions.php:1) in /var/www/html/wp-admin/admin-ajax.php on line 28
[02-Aug-2025 18:26:14 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /var/www/html/wp-content/themes/wpresidence-child/functions.php:1) in /var/www/html/wp-includes/functions.php on line 7031
[02-Aug-2025 18:26:14 UTC] PHP Deprecated:  Constant FILTER_SANITIZE_STRING is deprecated in /var/www/html/wp-content/plugins/chaty-pro/admin/class-admin-base.php on line 801
[02-Aug-2025 18:26:14 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /var/www/html/wp-content/themes/wpresidence-child/functions.php:1) in /var/www/html/wp-includes/functions.php on line 7168
[02-Aug-2025 18:26:14 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /var/www/html/wp-content/themes/wpresidence-child/functions.php:1) in /var/www/html/wp-includes/functions.php on line 7144
[02-Aug-2025 18:26:15 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /var/www/html/wp-content/themes/wpresidence-child/functions.php:1) in /var/www/html/wp-includes/class-wp-ajax-response.php on line 153
