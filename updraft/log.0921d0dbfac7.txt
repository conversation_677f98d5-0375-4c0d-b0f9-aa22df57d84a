0000.000 (0) Opened log file at time: Sat, 02 Aug 2025 18:50:27 +0000 on http://uptrend.site
0000.005 (0) UpdraftPlus WordPress backup plugin (https://updraftplus.com): 1.25.6 WP: 6.8.2 PHP: 8.1.32 (a<PERSON><PERSON><PERSON><PERSON><PERSON>, Linux 4c429eaf0cd2 6.14.0-27-generic #27-Ubuntu SMP PREEMPT_DYNAMIC Tue Jul 22 17:01:58 UTC 2025 x86_64) MySQL: 8.4.4 (max packet size=67108864) WPLANG: ru_RU Server: Apache/2.4.62 (Debian) safe_mode: 0 max_execution_time: 900 memory_limit: 1G (used: 12.6M | 28M) multisite: N openssl: OpenSSL 3.0.15 3 Sep 2024 mcrypt: N LANG: C WP Proxy: disabled ZipArchive::addFile: Y
0000.008 (0) Free space on disk containing Updraft's temporary directory: 420651 MB
0000.014 (0) Tasks: Backup files: 1 (schedule: every4hours) Backup DB:  (schedule: every4hours)
0000.016 (0) Processed schedules. Combining jobs from identical schedules. Tasks now: Backup files: 1 Backup DB: 1
0000.029 (0) Requesting semaphore lock (fd) (apparently via scheduler: last_scheduled_action_called_at=1754146356, seconds_ago=14271)
0000.041 (0) Set semaphore last lock (fd) time to 2025-08-02 18:50:27
0000.044 (0) Semaphore lock (fd) complete
0000.053 (0) Backup run: resumption=0, nonce=0921d0dbfac7, file_nonce=0921d0dbfac7 begun at=1754160627 (0s ago), job type=backup
0000.060 (0) Scheduling a resumption (1) after 300 seconds (1754160927) in case this run gets aborted
0000.077 (0) Checking if we have a zip executable available
0000.084 (0) Creation of backups of directories: beginning
0000.090 (0) No backup of plugins: excluded by user's options
0000.096 (0) Beginning creation of dump of themes (split every: 25 MB)
0000.164 (0) Total entities for the zip file: 213 directories, 1774 files (0 skipped as non-modified), 170.6 MB
0000.171 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (26.6 MB, 1774 files batched, 49 (49) added so far); re-opening (prior size: 0 KB)
0000.511 (0) A useful amount of data was added after this amount of zip processing: 1.1 s (normalised: 1 s, rate: 25578.9 KB/s)
0000.515 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=1.0642569065094, normalised_time=1.000825885604, max_time=0.51467514038086, data points known=1, max_bytes=26214400)
0000.523 (0) Zip size is at/near split limit (26.5 MB / 25 MB) - bumping index (from: 0)
0000.766 (0) Creating zip file manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes.zip.list-temp.tmp)
0000.770 (0) Successfully created zip file manifest (size: 3795)
0000.779 (0) Created themes zip (0) - 27096.7 KB in 0.4 s (64004.1 KB/s) (checksums: sha1: 718bd809df11eb1085938c3c1dd3b7d0e13ca1c0, sha256: fc228b53869959423f77c44d89df91a4f6d0558130049e9fb75ad0c27837ef84)
0000.800 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0000.803 (0) No remote despatch: user chose no remote backup service
0000.807 (0) Recording as successfully uploaded: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes.zip
0000.815 (0) Deleting zip manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes.zip.list.tmp)
0000.818 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0000.827 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (31.5 MB, 1774 files batched, 22 (71) added so far); re-opening (prior size: 0 KB)
0001.889 (0) A useful amount of data was added after this amount of zip processing: 1.4 s (normalised: 1.1 s, rate: 22395.4 KB/s)
0001.893 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=1.4425721168518, normalised_time=1.1430915081688, max_time=1.8925590515137, data points known=1, max_bytes=26214400)
0001.899 (0) Zip size is at/near split limit (31.6 MB / 25 MB) - bumping index (from: 1)
0002.181 (0) Creating zip file manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes2.zip.list-temp.tmp)
0002.185 (0) Successfully created zip file manifest (size: 2270)
0002.193 (0) Created themes zip (1) - 32313.6 KB in 1.1 s (28848.8 KB/s) (checksums: sha1: abc8b7081621fba45929380133a585413de360a2, sha256: a8811136b07fa86cba3e20e454a97775eba97cf8b774a7a89b60a13ca27ed02b)
0002.214 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0002.216 (0) No remote despatch: user chose no remote backup service
0002.219 (0) Recording as successfully uploaded: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes2.zip
0002.227 (0) Deleting zip manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes2.zip.list.tmp)
0002.230 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0002.238 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes3.zip.tmp: 100 files added (on-disk size: 0 KB)
0002.244 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (28 MB, 1774 files batched, 80 (151) added so far); re-opening (prior size: 0 KB)
0002.929 (0) A useful amount of data was added after this amount of zip processing: 1.5 s (normalised: 1.3 s, rate: 19367.4 KB/s)
0002.941 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=1.4823129177094, normalised_time=1.3218078835964, max_time=2.9404871463776, data points known=1, max_bytes=26214400)
0002.949 (0) Zip size is at/near split limit (28.1 MB / 25 MB) - bumping index (from: 2)
0003.177 (0) Creating zip file manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes3.zip.list-temp.tmp)
0003.181 (0) Successfully created zip file manifest (size: 7716)
0003.188 (0) Created themes zip (2) - 28728 KB in 0.8 s (38028 KB/s) (checksums: sha1: 38a4ea492d4ac60e91587cdde96b80f5d1c69541, sha256: 4d530de7eaa156e5c62a71b8d2f7ac048c48e302f9724605d28862bf911c6c51)
0003.212 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0003.216 (0) No remote despatch: user chose no remote backup service
0003.220 (0) Recording as successfully uploaded: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes3.zip
0003.227 (0) Deleting zip manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes3.zip.list.tmp)
0003.231 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0003.239 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes4.zip.tmp: 200 files added (on-disk size: 0 KB)
0003.247 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (25 MB, 1774 files batched, 102 (253) added so far); re-opening (prior size: 0 KB)
0003.382 (0) A useful amount of data was added after this amount of zip processing: 0.9 s (normalised: 0.9 s, rate: 27403.3 KB/s)
0003.386 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=0.93543601036072, normalised_time=0.93419375190743, max_time=3.3853561878204, data points known=1, max_bytes=26214400)
0003.394 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): possibly approaching split limit (0.2 MB, 1 (254) files added so far); last ratio: 0.9974; re-opening (prior size: 25566.7 KB)
0003.484 (0) Zip size is at/near split limit (25.2 MB / 25 MB) - bumping index (from: 3)
0003.690 (0) Creating zip file manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes4.zip.list-temp.tmp)
0003.694 (0) Successfully created zip file manifest (size: 7668)
0003.703 (0) Created themes zip (3) - 25810.6 KB in 0.3 s (87708.1 KB/s) (checksums: sha1: 23d2de251a31b0c5d7068ed92f4aa8d0284a578f, sha256: 19812a5776a129af6bcb2da1e50e0b266d9128db20f7a1e9ce9b7b5873d6f4ae)
0003.726 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0003.729 (0) No remote despatch: user chose no remote backup service
0003.733 (0) Recording as successfully uploaded: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes4.zip
0003.740 (0) Deleting zip manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes4.zip.list.tmp)
0003.743 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0003.754 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 300 files added (on-disk size: 0 KB)
0003.764 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 400 files added (on-disk size: 0 KB)
0003.773 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 500 files added (on-disk size: 0 KB)
0003.782 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 600 files added (on-disk size: 0 KB)
0003.790 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 700 files added (on-disk size: 0 KB)
0003.799 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 800 files added (on-disk size: 0 KB)
0003.808 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 900 files added (on-disk size: 0 KB)
0003.816 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 1000 files added (on-disk size: 0 KB)
0003.820 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (25 MB, 1774 files batched, 749 (1003) added so far); re-opening (prior size: 0 KB)
0005.516 (0) A useful amount of data was added after this amount of zip processing: 2.1 s (normalised: 2.1 s, rate: 12371.7 KB/s)
0005.519 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=2.0693860054016, normalised_time=2.0692340554979, max_time=5.5190811157227, data points known=1, max_bytes=26214400)
0005.538 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 1100 files added (on-disk size: 11394.9 KB)
0005.547 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 1200 files added (on-disk size: 11394.9 KB)
0005.555 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 1300 files added (on-disk size: 11394.9 KB)
0005.563 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 1400 files added (on-disk size: 11394.9 KB)
0005.572 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.tmp: 1500 files added (on-disk size: 11394.9 KB)
0005.578 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (28.9 MB, 1774 files batched, 543 (1546) added so far); re-opening (prior size: 11394.9 KB)
0006.850 (0) A useful amount of data was added after this amount of zip processing: 1.4 s (normalised: 1.2 s, rate: 21087.5 KB/s)
0006.853 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=1.403235912323, normalised_time=1.2139887290588, max_time=6.8522651195526, data points known=1, max_bytes=26214400)
0006.859 (0) Zip size is at/near split limit (31.3 MB / 25 MB) - bumping index (from: 4)
0007.127 (0) Creating zip file manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.list-temp.tmp)
0007.138 (0) Successfully created zip file manifest (size: 89575)
0007.146 (0) Created themes zip (4) - 32037 KB in 3.2 s (10152.9 KB/s) (checksums: sha1: 45d1d79e7fd636b8e2c7398d16eec2becd9487d6, sha256: 6a1e7454cc959d3b8c3a644846d78a0d3e8d0905248f4d9243de7804dc9cfd94)
0007.170 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0007.174 (0) No remote despatch: user chose no remote backup service
0007.177 (0) Recording as successfully uploaded: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip
0007.184 (0) Deleting zip manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip.list.tmp)
0007.187 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0007.198 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes6.zip.tmp: 1600 files added (on-disk size: 0 KB)
0007.206 (0) Zip: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes6.zip.tmp: 1700 files added (on-disk size: 0 KB)
0007.588 (0) Creating zip file manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes6.zip.list-temp.tmp)
0007.593 (0) Successfully created zip file manifest (size: 13293)
0007.621 (0) Created themes zip (5) - 1425.7 KB in 0.5 s (3056.5 KB/s) (sha1: 70148f9ba661511d3f37c7f981d3bdc499a08181, sha256: 76a44891d7d7e2daf93e202416690e491d02499a4921c4c0a90a152599bb52b7)
0007.637 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0007.640 (0) No remote despatch: user chose no remote backup service
0007.643 (0) Recording as successfully uploaded: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes6.zip
0007.650 (0) Deleting zip manifest (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes6.zip.list.tmp)
0007.655 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0007.668 (0) No backup of uploads: excluded by user's options
0007.671 (0) No backup of mu-plugins: excluded by user's options
0007.673 (0) No backup of others: excluded by user's options
0007.687 (0) Saving backup status to database (elements: 7)
0007.713 (0) Beginning creation of database dump (WordPress DB)
0007.717 (0) SQL compatibility mode is: NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
0007.747 (0) Table wp_options: Total expected rows (approximate): 1033
0007.855 (0) Table wp_options: Rows added in this batch (next record: 509972): 1044 (uncompressed bytes in this segment=2714211) in 0.12 seconds
0007.872 (0) Table wp_options: finishing file(s) (2, 597.1 KB)
0007.884 (0) Table wp_users: Total expected rows (via COUNT): 3
0007.887 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0007.894 (0) Table wp_users: Rows added in this batch (next record: 16): 3 (uncompressed bytes in this segment=1688) in 0.01 seconds
0007.906 (0) Table wp_users: finishing file(s) (2, 0.7 KB)
0007.922 (0) Table wp_usermeta: Total expected rows (via COUNT): 211
0007.925 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0007.935 (0) Table wp_usermeta: Rows added in this batch (next record: 873): 211 (uncompressed bytes in this segment=18910) in 0.02 seconds
0007.951 (0) Table wp_usermeta: finishing file(s) (2, 4.8 KB)
0007.967 (0) Table wp_actionscheduler_actions: Total expected rows (via COUNT): 20
0007.970 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0007.979 (0) Table wp_actionscheduler_actions: Rows added in this batch (next record: 43212): 20 (uncompressed bytes in this segment=9244) in 0.02 seconds
0007.996 (0) Table wp_actionscheduler_actions: finishing file(s) (2, 1.6 KB)
0008.006 (0) Table wp_actionscheduler_claims: Total expected rows (via COUNT): 0
0008.013 (0) Table wp_actionscheduler_claims: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=633) in 0.01 seconds
0008.026 (0) Table wp_actionscheduler_claims: finishing file(s) (2, 0.4 KB)
0008.036 (0) Table wp_actionscheduler_groups: Total expected rows (via COUNT): 3
0008.040 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.048 (0) Table wp_actionscheduler_groups: Rows added in this batch (next record: 3): 3 (uncompressed bytes in this segment=718) in 0.01 seconds
0008.060 (0) Table wp_actionscheduler_groups: finishing file(s) (2, 0.4 KB)
0008.071 (0) Table wp_actionscheduler_logs: Total expected rows (via COUNT): 56
0008.075 (0) Table is relatively small; fetch_rows will thus be: 100 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.084 (0) Table wp_actionscheduler_logs: Rows added in this batch (next record: 127966): 56 (uncompressed bytes in this segment=8108) in 0.01 seconds
0008.098 (0) Table wp_actionscheduler_logs: finishing file(s) (2, 1.5 KB)
0008.116 (0) Table wp_commentmeta: Total expected rows (via COUNT): 3
0008.119 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.128 (0) Table wp_commentmeta: Rows added in this batch (next record: 32): 3 (uncompressed bytes in this segment=869) in 0.02 seconds
0008.146 (0) Table wp_commentmeta: finishing file(s) (2, 0.5 KB)
0008.156 (0) Table wp_comments: Total expected rows (via COUNT): 1
0008.159 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.163 (0) Table wp_comments: Rows added in this batch (next record: 9): 1 (uncompressed bytes in this segment=2339) in 0.01 seconds
0008.175 (0) Table wp_comments: finishing file(s) (2, 0.9 KB)
0008.188 (0) Table wp_links: Total expected rows (via COUNT): 0
0008.196 (0) Table wp_links: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1298) in 0.01 seconds
0008.208 (0) Table wp_links: finishing file(s) (2, 0.5 KB)
0008.946 (0) Table wp_postmeta: Total expected rows (approximate): 70505
0012.636 (0) Table wp_postmeta: Rows added in this batch (next record: 77314): 48000 (uncompressed bytes in this segment=118528591) in 4.42 seconds
0012.690 (0) Table wp_postmeta: Total expected rows (via COUNT): 94904
0013.495 (0) Table wp_postmeta: Rows added in this batch (next record: 125734): 46904 (uncompressed bytes in this segment=18281704) in 0.84 seconds
0013.516 (0) Table wp_postmeta: finishing file(s) (3, 13996 KB)
0013.935 (0) Table wp_posts: Total expected rows (approximate): 5676
0017.283 (0) Table wp_posts: Rows added in this batch (next record: 35205): 2603 (uncompressed bytes in this segment=107282324) in 3.76 seconds
0017.305 (0) Table wp_posts: Total expected rows (via COUNT): 6102
0017.727 (0) Table wp_posts: Rows added in this batch (next record: 38897): 3499 (uncompressed bytes in this segment=8543461) in 0.43 seconds
0017.753 (0) Table wp_posts: finishing file(s) (3, 14985.7 KB)
0017.765 (0) Table wp_term_relationships: Total expected rows (approximate): 3175
0017.801 (0) Table wp_term_relationships: Rows added in this batch (next record: 100000): 3336 (uncompressed bytes in this segment=60409) in 0.04 seconds
0017.816 (0) Table wp_term_relationships: finishing file(s) (2, 9.4 KB)
0017.833 (0) Table wp_term_taxonomy: Total expected rows (via COUNT): 245
0017.837 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0017.848 (0) Table wp_term_taxonomy: Rows added in this batch (next record: 321): 245 (uncompressed bytes in this segment=13936) in 0.02 seconds
0017.865 (0) Table wp_term_taxonomy: finishing file(s) (2, 3.7 KB)
0018.105 (0) Table wp_termmeta: Total expected rows (via COUNT): 0
0018.116 (0) Table wp_termmeta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=650) in 0.16 seconds
0018.134 (0) Table wp_termmeta: finishing file(s) (2, 0.4 KB)
0018.146 (0) Table wp_terms: Total expected rows (via COUNT): 245
0018.150 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0018.162 (0) Table wp_terms: Rows added in this batch (next record: 321): 245 (uncompressed bytes in this segment=16429) in 0.02 seconds
0018.177 (0) Table wp_terms: finishing file(s) (2, 4.7 KB)
0018.196 (0) Table wp_aioseo_cache: Total expected rows (via COUNT): 16
0018.200 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0018.212 (0) Table wp_aioseo_cache: Rows added in this batch (next record: 15024): 16 (uncompressed bytes in this segment=98710) in 0.02 seconds
0018.231 (0) Table wp_aioseo_cache: finishing file(s) (2, 19.1 KB)
0018.244 (0) Table wp_aioseo_crawl_cleanup_blocked_args: Total expected rows (via COUNT): 0
0018.253 (0) Table wp_aioseo_crawl_cleanup_blocked_args: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1039) in 0.01 seconds
0018.271 (0) Table wp_aioseo_crawl_cleanup_blocked_args: finishing file(s) (2, 0.4 KB)
0018.285 (0) Table wp_aioseo_crawl_cleanup_logs: Total expected rows (via COUNT): 0
0018.290 (0) Table wp_aioseo_crawl_cleanup_logs: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=875) in 0.01 seconds
0018.303 (0) Table wp_aioseo_crawl_cleanup_logs: finishing file(s) (2, 0.4 KB)
0018.321 (0) Table wp_aioseo_notifications: Total expected rows (via COUNT): 40
0018.324 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0018.349 (0) Table wp_aioseo_notifications: Rows added in this batch (next record: 40): 40 (uncompressed bytes in this segment=287537) in 0.04 seconds
0018.367 (0) Table wp_aioseo_notifications: finishing file(s) (2, 197.8 KB)
0018.415 (0) Table wp_aioseo_posts: Total expected rows (approximate): 4191
0018.949 (0) Table wp_aioseo_posts: Rows added in this batch (next record: 4918): 4835 (uncompressed bytes in this segment=8104745) in 0.57 seconds
0018.967 (0) Table wp_aioseo_posts: finishing file(s) (2, 114.1 KB)
0018.980 (0) Table wp_chaty_contact_form_leads: Total expected rows (via COUNT): 0
0018.984 (0) Table wp_chaty_contact_form_leads: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=958) in 0.01 seconds
0018.996 (0) Table wp_chaty_contact_form_leads: finishing file(s) (2, 0.4 KB)
0019.010 (0) Table wp_chaty_widget_analysis: Total expected rows (approximate): 2261
0019.050 (0) Table wp_chaty_widget_analysis: Rows added in this batch (next record: 2337): 2337 (uncompressed bytes in this segment=99339) in 0.04 seconds
0019.065 (0) Table wp_chaty_widget_analysis: finishing file(s) (2, 12.9 KB)
0019.083 (0) Table wp_db7_forms: Total expected rows (via COUNT): 0
0019.086 (0) Table wp_db7_forms: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=567) in 0.01 seconds
0019.103 (0) Table wp_db7_forms: finishing file(s) (2, 0.4 KB)
0019.115 (0) Table wp_e_events: Total expected rows (via COUNT): 1
0019.120 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0019.129 (0) Table wp_e_events: Rows added in this batch (next record: 1): 1 (uncompressed bytes in this segment=787) in 0.02 seconds
0019.143 (0) Table wp_e_events: finishing file(s) (2, 0.5 KB)
0019.155 (0) Table wp_e_submissions: Total expected rows (approximate): 6911
0019.535 (0) Table wp_e_submissions: Rows added in this batch (next record: 7127): 7127 (uncompressed bytes in this segment=3677283) in 0.38 seconds
0019.548 (0) Table wp_e_submissions: finishing file(s) (2, 385.8 KB)
0019.561 (0) Table wp_e_submissions_actions_log: Total expected rows (approximate): 6924
0019.754 (0) Table wp_e_submissions_actions_log: Rows added in this batch (next record: 7115): 7115 (uncompressed bytes in this segment=1261322) in 0.20 seconds
0019.768 (0) Table wp_e_submissions_actions_log: finishing file(s) (2, 119.6 KB)
0020.018 (0) Table wp_e_submissions_values: Total expected rows (approximate): 38837
0020.793 (0) Table wp_e_submissions_values: Rows added in this batch (next record: 35565): 35565 (uncompressed bytes in this segment=10380213) in 1.01 seconds
0020.815 (0) Table wp_e_submissions_values: finishing file(s) (2, 2699.2 KB)
0020.835 (0) Table wp_eacf7_draft_submissions: Total expected rows (via COUNT): 0
0020.840 (0) Table wp_eacf7_draft_submissions: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=907) in 0.01 seconds
0020.856 (0) Table wp_eacf7_draft_submissions: finishing file(s) (2, 0.4 KB)
0020.873 (0) Table wp_eacf7_entries: Total expected rows (via COUNT): 0
0020.877 (0) Table wp_eacf7_entries: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=754) in 0.01 seconds
0020.894 (0) Table wp_eacf7_entries: finishing file(s) (2, 0.4 KB)
0020.913 (0) Table wp_mystickyelement_contact_lists: Total expected rows (via COUNT): 2
0020.916 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0020.924 (0) Table wp_mystickyelement_contact_lists: Rows added in this batch (next record: 2): 2 (uncompressed bytes in this segment=1618) in 0.02 seconds
0020.946 (0) Table wp_mystickyelement_contact_lists: finishing file(s) (2, 0.7 KB)
0020.958 (0) Table wp_odb_logs: Total expected rows (via COUNT): 0
0020.966 (0) Table wp_odb_logs: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=782) in 0.01 seconds
0020.978 (0) Table wp_odb_logs: finishing file(s) (2, 0.4 KB)
0020.996 (0) Table wp_pmxe_exports: Total expected rows (via COUNT): 1
0021.000 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.011 (0) Table wp_pmxe_exports: Rows added in this batch (next record: 1): 1 (uncompressed bytes in this segment=84320) in 0.02 seconds
0021.028 (0) Table wp_pmxe_exports: finishing file(s) (2, 14.8 KB)
0021.040 (0) Table wp_pmxe_google_cats: Total expected rows (approximate): 5407
0021.128 (0) Table wp_pmxe_google_cats: Rows added in this batch (next record: 505832): 5371 (uncompressed bytes in this segment=327566) in 0.09 seconds
0021.143 (0) Table wp_pmxe_google_cats: finishing file(s) (2, 75.3 KB)
0021.157 (0) Table wp_pmxe_posts: Total expected rows (via COUNT): 144
0021.160 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.172 (0) Table wp_pmxe_posts: Rows added in this batch (next record: 144): 144 (uncompressed bytes in this segment=3503) in 0.02 seconds
0021.187 (0) Table wp_pmxe_posts: finishing file(s) (2, 1.1 KB)
0021.203 (0) Table wp_pmxe_templates: Total expected rows (via COUNT): 0
0021.213 (0) Table wp_pmxe_templates: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=561) in 0.02 seconds
0021.239 (0) Table wp_pmxe_templates: finishing file(s) (2, 0.3 KB)
0021.256 (0) Table wp_revslider_css: Total expected rows (via COUNT): 109
0021.258 (0) Table is relatively small; fetch_rows will thus be: 200 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.268 (0) Table wp_revslider_css: Rows added in this batch (next record: 109): 109 (uncompressed bytes in this segment=91061) in 0.02 seconds
0021.283 (0) Table wp_revslider_css: finishing file(s) (2, 4.9 KB)
0021.302 (0) Table wp_revslider_css_bkp: Total expected rows (via COUNT): 47
0021.305 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.313 (0) Table wp_revslider_css_bkp: Rows added in this batch (next record: 47): 47 (uncompressed bytes in this segment=15390) in 0.02 seconds
0021.331 (0) Table wp_revslider_css_bkp: finishing file(s) (2, 1.8 KB)
0021.346 (0) Table wp_revslider_layer_animations: Total expected rows (via COUNT): 0
0021.353 (0) Table wp_revslider_layer_animations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=533) in 0.01 seconds
0021.365 (0) Table wp_revslider_layer_animations: finishing file(s) (2, 0.3 KB)
0021.377 (0) Table wp_revslider_layer_animations_bkp: Total expected rows (via COUNT): 0
0021.381 (0) Table wp_revslider_layer_animations_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=557) in 0.01 seconds
0021.393 (0) Table wp_revslider_layer_animations_bkp: finishing file(s) (2, 0.3 KB)
0021.410 (0) Table wp_revslider_navigations: Total expected rows (via COUNT): 0
0021.414 (0) Table wp_revslider_navigations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=610) in 0.01 seconds
0021.431 (0) Table wp_revslider_navigations: finishing file(s) (2, 0.3 KB)
0021.448 (0) Table wp_revslider_navigations_bkp: Total expected rows (via COUNT): 0
0021.452 (0) Table wp_revslider_navigations_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=634) in 0.01 seconds
0021.469 (0) Table wp_revslider_navigations_bkp: finishing file(s) (2, 0.3 KB)
0021.487 (0) Table wp_revslider_sliders: Total expected rows (via COUNT): 1
0021.490 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.498 (0) Table wp_revslider_sliders: Rows added in this batch (next record: 1): 1 (uncompressed bytes in this segment=9624) in 0.02 seconds
0021.515 (0) Table wp_revslider_sliders: finishing file(s) (2, 3.2 KB)
0021.537 (0) Table wp_revslider_sliders_bkp: Total expected rows (via COUNT): 0
0021.557 (0) Table wp_revslider_sliders_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=605) in 0.03 seconds
0021.604 (0) Table wp_revslider_sliders_bkp: finishing file(s) (2, 0.4 KB)
0021.645 (0) Table wp_revslider_slides: Total expected rows (via COUNT): 6
0021.649 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.658 (0) Table wp_revslider_slides: Rows added in this batch (next record: 6): 6 (uncompressed bytes in this segment=8227) in 0.03 seconds
0021.675 (0) Table wp_revslider_slides: finishing file(s) (2, 1 KB)
0021.695 (0) Table wp_revslider_slides_bkp: Total expected rows (via COUNT): 0
0021.703 (0) Table wp_revslider_slides_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=611) in 0.02 seconds
0021.719 (0) Table wp_revslider_slides_bkp: finishing file(s) (2, 0.3 KB)
0021.740 (0) Table wp_revslider_static_slides: Total expected rows (via COUNT): 1
0021.743 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.752 (0) Table wp_revslider_static_slides: Rows added in this batch (next record: 1): 1 (uncompressed bytes in this segment=700) in 0.02 seconds
0021.770 (0) Table wp_revslider_static_slides: finishing file(s) (2, 0.4 KB)
0021.787 (0) Table wp_revslider_static_slides_bkp: Total expected rows (via COUNT): 0
0021.795 (0) Table wp_revslider_static_slides_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=623) in 0.02 seconds
0021.811 (0) Table wp_revslider_static_slides_bkp: finishing file(s) (2, 0.3 KB)
0021.831 (0) Table wp_snippets: Total expected rows (via COUNT): 5
0021.834 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.843 (0) Table wp_snippets: Rows added in this batch (next record: 5): 5 (uncompressed bytes in this segment=3107) in 0.02 seconds
0021.861 (0) Table wp_snippets: finishing file(s) (2, 1.2 KB)
0021.876 (0) Table wp_statistics_events: Total expected rows (via COUNT): 0
0021.885 (0) Table wp_statistics_events: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=768) in 0.01 seconds
0021.899 (0) Table wp_statistics_events: finishing file(s) (2, 0.4 KB)
0021.914 (0) Table wp_statistics_exclusions: Total expected rows (via COUNT): 0
0021.919 (0) Table wp_statistics_exclusions: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=637) in 0.01 seconds
0021.933 (0) Table wp_statistics_exclusions: finishing file(s) (2, 0.4 KB)
0021.950 (0) Table wp_statistics_historical: Total expected rows (via COUNT): 0
0021.955 (0) Table wp_statistics_historical: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=710) in 0.01 seconds
0021.971 (0) Table wp_statistics_historical: finishing file(s) (2, 0.4 KB)
0021.985 (0) Table wp_statistics_pages: Total expected rows (approximate): 5824
0022.087 (0) Table wp_statistics_pages: Rows added in this batch (next record: 5849): 5832 (uncompressed bytes in this segment=579450) in 0.11 seconds
0022.099 (0) Table wp_statistics_pages: finishing file(s) (2, 66.8 KB)
0022.113 (0) Table wp_statistics_search: Total expected rows (approximate): 1425
0022.141 (0) Table wp_statistics_search: Rows added in this batch (next record: 1425): 1425 (uncompressed bytes in this segment=94902) in 0.03 seconds
0022.154 (0) Table wp_statistics_search: finishing file(s) (2, 12.2 KB)
0022.168 (0) Table wp_statistics_useronline: Total expected rows (via COUNT): 0
0022.171 (0) Table wp_statistics_useronline: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1125) in 0.01 seconds
0022.184 (0) Table wp_statistics_useronline: finishing file(s) (2, 0.5 KB)
0022.197 (0) Table wp_statistics_visit: Total expected rows (via COUNT): 612
0022.209 (0) Table wp_statistics_visit: Rows added in this batch (next record: 2686): 612 (uncompressed bytes in this segment=30865) in 0.02 seconds
0022.222 (0) Table wp_statistics_visit: finishing file(s) (2, 8.1 KB)
0022.235 (0) Table wp_statistics_visitor: Total expected rows (approximate): 8824
0022.551 (0) Table wp_statistics_visitor: Rows added in this batch (next record: 9055): 9041 (uncompressed bytes in this segment=1682962) in 0.32 seconds
0022.564 (0) Table wp_statistics_visitor: finishing file(s) (2, 341.3 KB)
0022.578 (0) Table wp_statistics_visitor_relationships: Total expected rows (via COUNT): 0
0022.581 (0) Table wp_statistics_visitor_relationships: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=686) in 0.01 seconds
0022.593 (0) Table wp_statistics_visitor_relationships: finishing file(s) (2, 0.4 KB)
0022.614 (0) Table wp_tm_taskmeta: Total expected rows (approximate): 1862
0022.646 (0) Table wp_tm_taskmeta: Rows added in this batch (next record: 2007): 2007 (uncompressed bytes in this segment=408476) in 0.05 seconds
0022.663 (0) Table wp_tm_taskmeta: finishing file(s) (2, 26.4 KB)
0022.675 (0) Table wp_tm_tasks: Total expected rows (approximate): 1812
0022.729 (0) Table wp_tm_tasks: Rows added in this batch (next record: 2007): 2007 (uncompressed bytes in this segment=381261) in 0.06 seconds
0022.743 (0) Table wp_tm_tasks: finishing file(s) (2, 18 KB)
0022.797 (0) Table wp_trp_dictionary_ru_ru_en_us: Total expected rows (approximate): 25425
0023.278 (0) Table wp_trp_dictionary_ru_ru_en_us: Rows added in this batch (next record: 24731): 24441 (uncompressed bytes in this segment=5173730) in 0.52 seconds
0023.390 (0) Table wp_trp_dictionary_ru_ru_en_us: finishing file(s) (2, 1014.3 KB)
0023.581 (0) Table wp_trp_dictionary_ru_ru_tr_tr: Total expected rows (approximate): 23957
0024.154 (0) Table wp_trp_dictionary_ru_ru_tr_tr: Rows added in this batch (next record: 24759): 24377 (uncompressed bytes in this segment=5021320) in 0.71 seconds
0024.422 (0) Table wp_trp_dictionary_ru_ru_tr_tr: finishing file(s) (2, 994.7 KB)
0024.704 (0) Table wp_trp_dictionary_ru_ru_uk: Total expected rows (approximate): 24700
0025.237 (0) Table wp_trp_dictionary_ru_ru_uk: Rows added in this batch (next record: 24575): 24328 (uncompressed bytes in this segment=5405708) in 0.71 seconds
0025.261 (0) Table wp_trp_dictionary_ru_ru_uk: finishing file(s) (2, 980.7 KB)
0025.289 (0) Table wp_trp_dictionary_tr_tr_de_de: Total expected rows (approximate): 3520
0025.350 (0) Table wp_trp_dictionary_tr_tr_de_de: Rows added in this batch (next record: 3586): 3586 (uncompressed bytes in this segment=409263) in 0.08 seconds
0025.368 (0) Table wp_trp_dictionary_tr_tr_de_de: finishing file(s) (2, 97.6 KB)
0025.399 (0) Table wp_trp_dictionary_tr_tr_en_us: Total expected rows (approximate): 6361
0025.510 (0) Table wp_trp_dictionary_tr_tr_en_us: Rows added in this batch (next record: 6500): 6500 (uncompressed bytes in this segment=836813) in 0.13 seconds
0025.532 (0) Table wp_trp_dictionary_tr_tr_en_us: finishing file(s) (2, 194.5 KB)
0025.565 (0) Table wp_trp_dictionary_tr_tr_ru_ru: Total expected rows (approximate): 7113
0025.748 (0) Table wp_trp_dictionary_tr_tr_ru_ru: Rows added in this batch (next record: 6839): 6839 (uncompressed bytes in this segment=912966) in 0.21 seconds
0025.764 (0) Table wp_trp_dictionary_tr_tr_ru_ru: finishing file(s) (2, 204.5 KB)
0025.794 (0) Table wp_trp_dictionary_tr_tr_uk: Total expected rows (approximate): 6357
0025.978 (0) Table wp_trp_dictionary_tr_tr_uk: Rows added in this batch (next record: 6739): 6739 (uncompressed bytes in this segment=905812) in 0.20 seconds
0025.995 (0) Table wp_trp_dictionary_tr_tr_uk: finishing file(s) (2, 204.5 KB)
0026.011 (0) Table wp_trp_dictionary_tr_tr_zh_cn: Total expected rows (via COUNT): 10
0026.013 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0026.021 (0) Table wp_trp_dictionary_tr_tr_zh_cn: Rows added in this batch (next record: 10): 10 (uncompressed bytes in this segment=3586) in 0.02 seconds
0026.036 (0) Table wp_trp_dictionary_tr_tr_zh_cn: finishing file(s) (2, 1.5 KB)
0026.063 (0) Table wp_trp_gettext_de_de: Total expected rows (approximate): 2278
0026.114 (0) Table wp_trp_gettext_de_de: Rows added in this batch (next record: 2278): 2278 (uncompressed bytes in this segment=161114) in 0.07 seconds
0026.131 (0) Table wp_trp_gettext_de_de: finishing file(s) (2, 40.1 KB)
0026.155 (0) Table wp_trp_gettext_en_us: Total expected rows (approximate): 3040
0026.217 (0) Table wp_trp_gettext_en_us: Rows added in this batch (next record: 3163): 3163 (uncompressed bytes in this segment=220817) in 0.08 seconds
0026.235 (0) Table wp_trp_gettext_en_us: finishing file(s) (2, 51.1 KB)
0026.257 (0) Table wp_trp_gettext_original_meta: Total expected rows (via COUNT): 0
0026.266 (0) Table wp_trp_gettext_original_meta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=787) in 0.02 seconds
0026.284 (0) Table wp_trp_gettext_original_meta: finishing file(s) (2, 0.4 KB)
0026.297 (0) Table wp_trp_gettext_original_strings: Total expected rows (approximate): 3459
0026.372 (0) Table wp_trp_gettext_original_strings: Rows added in this batch (next record: 3740): 3682 (uncompressed bytes in this segment=258065) in 0.08 seconds
0026.386 (0) Table wp_trp_gettext_original_strings: finishing file(s) (2, 46.9 KB)
0026.410 (0) Table wp_trp_gettext_ru_ru: Total expected rows (approximate): 3324
0026.487 (0) Table wp_trp_gettext_ru_ru: Rows added in this batch (next record: 3562): 3562 (uncompressed bytes in this segment=388179) in 0.09 seconds
0026.505 (0) Table wp_trp_gettext_ru_ru: finishing file(s) (2, 98.1 KB)
0026.528 (0) Table wp_trp_gettext_tr_tr: Total expected rows (approximate): 3036
0026.593 (0) Table wp_trp_gettext_tr_tr: Rows added in this batch (next record: 3121): 3121 (uncompressed bytes in this segment=270901) in 0.08 seconds
0026.611 (0) Table wp_trp_gettext_tr_tr: finishing file(s) (2, 76.1 KB)
0026.636 (0) Table wp_trp_gettext_uk: Total expected rows (approximate): 2482
0026.694 (0) Table wp_trp_gettext_uk: Rows added in this batch (next record: 2657): 2657 (uncompressed bytes in this segment=283109) in 0.07 seconds
0026.712 (0) Table wp_trp_gettext_uk: finishing file(s) (2, 72.5 KB)
0026.729 (0) Table wp_trp_gettext_zh_cn: Total expected rows (via COUNT): 0
0026.732 (0) Table wp_trp_gettext_zh_cn: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=827) in 0.01 seconds
0026.748 (0) Table wp_trp_gettext_zh_cn: finishing file(s) (2, 0.4 KB)
0026.814 (0) Table wp_trp_original_meta: Total expected rows (approximate): 45040
0027.319 (0) Table wp_trp_original_meta: Rows added in this batch (next record: 45013): 45013 (uncompressed bytes in this segment=1936587) in 0.56 seconds
0027.343 (0) Table wp_trp_original_meta: finishing file(s) (2, 206.2 KB)
0027.355 (0) Table wp_trp_original_strings: Total expected rows (approximate): 35966
0027.742 (0) Table wp_trp_original_strings: Rows added in this batch (next record: 31226): 31226 (uncompressed bytes in this segment=5229954) in 0.39 seconds
0027.755 (0) Table wp_trp_original_strings: finishing file(s) (2, 787 KB)
0027.770 (0) Table wp_wpfm_backup: Total expected rows (via COUNT): 0
0027.774 (0) Table wp_wpfm_backup: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=510) in 0.01 seconds
0027.786 (0) Table wp_wpfm_backup: finishing file(s) (2, 0.3 KB)
0027.802 (0) Table wp_wpforms_logs: Total expected rows (via COUNT): 0
0027.806 (0) Table wp_wpforms_logs: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=736) in 0.01 seconds
0027.823 (0) Table wp_wpforms_logs: finishing file(s) (2, 0.4 KB)
0027.840 (0) Table wp_wpforms_payment_meta: Total expected rows (via COUNT): 0
0027.847 (0) Table wp_wpforms_payment_meta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=731) in 0.02 seconds
0027.863 (0) Table wp_wpforms_payment_meta: finishing file(s) (2, 0.4 KB)
0027.876 (0) Table wp_wpforms_payments: Total expected rows (via COUNT): 0
0027.884 (0) Table wp_wpforms_payments: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1958) in 0.01 seconds
0027.895 (0) Table wp_wpforms_payments: finishing file(s) (2, 0.6 KB)
0027.912 (0) Table wp_wpforms_tasks_meta: Total expected rows (via COUNT): 3
0027.914 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0027.922 (0) Table wp_wpforms_tasks_meta: Rows added in this batch (next record: 54): 3 (uncompressed bytes in this segment=898) in 0.02 seconds
0027.939 (0) Table wp_wpforms_tasks_meta: finishing file(s) (2, 0.5 KB)
0027.952 (0) Table wp_wpo_404_detector: Total expected rows (via COUNT): 0
0027.960 (0) Table wp_wpo_404_detector: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=857) in 0.01 seconds
0027.973 (0) Table wp_wpo_404_detector: finishing file(s) (2, 0.4 KB)
0027.976 (0) PHP event: code E_WARNING: filemtime(): stat failed for /var/www/html/wp-content/updraft/backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db.gz (line 1923, wp-content/plugins/updraftplus/backup.php)
0027.980 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_options.table.tmpr509973.gz (1/84/fopen): adding to final database dump
0027.997 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_options.table.gz (2/84/fopen): adding to final database dump
0028.000 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_users.table.tmpr17.gz (3/84/fopen): adding to final database dump
0028.004 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_users.table.gz (4/84/fopen): adding to final database dump
0028.007 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_usermeta.table.tmpr874.gz (5/84/fopen): adding to final database dump
0028.011 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_usermeta.table.gz (6/84/fopen): adding to final database dump
0028.014 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_actionscheduler_actions.table.tmpr43213.gz (7/84/fopen): adding to final database dump
0028.018 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_actionscheduler_actions.table.gz (8/84/fopen): adding to final database dump
0028.021 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_actionscheduler_claims.table.tmpr0.gz (9/84/fopen): adding to final database dump
0028.025 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_actionscheduler_claims.table.gz (10/84/fopen): adding to final database dump
0028.028 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_actionscheduler_groups.table.tmpr4.gz (11/84/fopen): adding to final database dump
0028.035 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_actionscheduler_groups.table.gz (12/84/fopen): adding to final database dump
0028.039 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_actionscheduler_logs.table.tmpr127967.gz (13/84/fopen): adding to final database dump
0028.043 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_actionscheduler_logs.table.gz (14/84/fopen): adding to final database dump
0028.046 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_commentmeta.table.tmpr33.gz (15/84/fopen): adding to final database dump
0028.050 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_commentmeta.table.gz (16/84/fopen): adding to final database dump
0028.053 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_comments.table.tmpr10.gz (17/84/fopen): adding to final database dump
0028.057 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_comments.table.gz (18/84/fopen): adding to final database dump
0028.060 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_links.table.tmpr0.gz (19/84/fopen): adding to final database dump
0028.064 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_links.table.gz (20/84/fopen): adding to final database dump
0028.067 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_postmeta.table.tmpr77314.gz (21/84/fopen): adding to final database dump
0028.325 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_postmeta.table.tmpr125735.gz (22/84/fopen): adding to final database dump
0028.373 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_postmeta.table.gz (23/84/fopen): adding to final database dump
0028.377 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_posts.table.tmpr35205.gz (24/84/fopen): adding to final database dump
0028.701 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_posts.table.tmpr38898.gz (25/84/fopen): adding to final database dump
0028.731 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_posts.table.gz (26/84/fopen): adding to final database dump
0028.735 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_term_relationships.table.tmpr100001.gz (27/84/fopen): adding to final database dump
0028.739 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_term_relationships.table.gz (28/84/fopen): adding to final database dump
0028.742 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_term_taxonomy.table.tmpr322.gz (29/84/fopen): adding to final database dump
0028.746 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_term_taxonomy.table.gz (30/84/fopen): adding to final database dump
0028.749 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_termmeta.table.tmpr0.gz (31/84/fopen): adding to final database dump
0028.754 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_termmeta.table.gz (32/84/fopen): adding to final database dump
0028.757 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_terms.table.tmpr322.gz (33/84/fopen): adding to final database dump
0028.761 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_terms.table.gz (34/84/fopen): adding to final database dump
0028.765 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_cache.table.tmpr15025.gz (35/84/fopen): adding to final database dump
0028.768 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_cache.table.gz (36/84/fopen): adding to final database dump
0028.772 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_crawl_cleanup_blocked_args.table.tmpr0.gz (37/84/fopen): adding to final database dump
0028.776 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_crawl_cleanup_blocked_args.table.gz (38/84/fopen): adding to final database dump
0028.779 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_crawl_cleanup_logs.table.tmpr0.gz (39/84/fopen): adding to final database dump
0028.784 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_crawl_cleanup_logs.table.gz (40/84/fopen): adding to final database dump
0028.789 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_notifications.table.tmpr41.gz (41/84/fopen): adding to final database dump
0028.797 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_notifications.table.gz (42/84/fopen): adding to final database dump
0028.800 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_posts.table.tmpr4919.gz (43/84/fopen): adding to final database dump
0028.809 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_aioseo_posts.table.gz (44/84/fopen): adding to final database dump
0028.813 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_chaty_contact_form_leads.table.tmpr0.gz (45/84/fopen): adding to final database dump
0028.816 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_chaty_contact_form_leads.table.gz (46/84/fopen): adding to final database dump
0028.820 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_chaty_widget_analysis.table.tmpr2338.gz (47/84/fopen): adding to final database dump
0028.825 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_chaty_widget_analysis.table.gz (48/84/fopen): adding to final database dump
0028.829 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_db7_forms.table.tmpr0.gz (49/84/fopen): adding to final database dump
0028.833 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_db7_forms.table.gz (50/84/fopen): adding to final database dump
0028.837 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_e_events.table.tmpr2.gz (51/84/fopen): adding to final database dump
0028.842 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_e_events.table.gz (52/84/fopen): adding to final database dump
0028.845 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_e_submissions.table.tmpr7128.gz (53/84/fopen): adding to final database dump
0028.855 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_e_submissions.table.gz (54/84/fopen): adding to final database dump
0028.859 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_e_submissions_actions_log.table.tmpr7116.gz (55/84/fopen): adding to final database dump
0028.865 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_e_submissions_actions_log.table.gz (56/84/fopen): adding to final database dump
0028.868 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_e_submissions_values.table.tmpr35566.gz (57/84/fopen): adding to final database dump
0028.928 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_e_submissions_values.table.gz (58/84/fopen): adding to final database dump
0028.932 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_eacf7_draft_submissions.table.tmpr0.gz (59/84/fopen): adding to final database dump
0028.935 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_eacf7_draft_submissions.table.gz (60/84/fopen): adding to final database dump
0028.938 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_eacf7_entries.table.tmpr0.gz (61/84/fopen): adding to final database dump
0028.941 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_eacf7_entries.table.gz (62/84/fopen): adding to final database dump
0028.943 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_mystickyelement_contact_lists.table.tmpr3.gz (63/84/fopen): adding to final database dump
0028.946 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_mystickyelement_contact_lists.table.gz (64/84/fopen): adding to final database dump
0028.949 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_odb_logs.table.tmpr0.gz (65/84/fopen): adding to final database dump
0028.953 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_odb_logs.table.gz (66/84/fopen): adding to final database dump
0028.956 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_pmxe_exports.table.tmpr2.gz (67/84/fopen): adding to final database dump
0028.960 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_pmxe_exports.table.gz (68/84/fopen): adding to final database dump
0028.963 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_pmxe_google_cats.table.tmpr505833.gz (69/84/fopen): adding to final database dump
0028.967 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_pmxe_google_cats.table.gz (70/84/fopen): adding to final database dump
0028.971 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_pmxe_posts.table.tmpr145.gz (71/84/fopen): adding to final database dump
0028.974 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_pmxe_posts.table.gz (72/84/fopen): adding to final database dump
0028.977 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_pmxe_templates.table.tmpr0.gz (73/84/fopen): adding to final database dump
0028.980 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_pmxe_templates.table.gz (74/84/fopen): adding to final database dump
0028.985 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_css.table.tmpr110.gz (75/84/fopen): adding to final database dump
0028.988 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_css.table.gz (76/84/fopen): adding to final database dump
0028.992 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_css_bkp.table.tmpr48.gz (77/84/fopen): adding to final database dump
0028.995 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_css_bkp.table.gz (78/84/fopen): adding to final database dump
0028.998 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_layer_animations.table.tmpr0.gz (79/84/fopen): adding to final database dump
0029.002 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_layer_animations.table.gz (80/84/fopen): adding to final database dump
0029.006 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_layer_animations_bkp.table.tmpr0.gz (81/84/fopen): adding to final database dump
0029.009 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_layer_animations_bkp.table.gz (82/84/fopen): adding to final database dump
0029.014 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_navigations.table.tmpr0.gz (83/84/fopen): adding to final database dump
0029.017 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_navigations.table.gz (84/84/fopen): adding to final database dump
0029.021 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_navigations_bkp.table.tmpr0.gz (85/84/fopen): adding to final database dump
0029.024 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_navigations_bkp.table.gz (86/84/fopen): adding to final database dump
0029.027 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_sliders.table.tmpr2.gz (87/84/fopen): adding to final database dump
0029.031 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_sliders.table.gz (88/84/fopen): adding to final database dump
0029.036 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_sliders_bkp.table.tmpr0.gz (89/84/fopen): adding to final database dump
0029.039 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_sliders_bkp.table.gz (90/84/fopen): adding to final database dump
0029.043 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_slides.table.tmpr7.gz (91/84/fopen): adding to final database dump
0029.047 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_slides.table.gz (92/84/fopen): adding to final database dump
0029.051 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_slides_bkp.table.tmpr0.gz (93/84/fopen): adding to final database dump
0029.054 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_slides_bkp.table.gz (94/84/fopen): adding to final database dump
0029.058 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_static_slides.table.tmpr2.gz (95/84/fopen): adding to final database dump
0029.061 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_static_slides.table.gz (96/84/fopen): adding to final database dump
0029.064 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_static_slides_bkp.table.tmpr0.gz (97/84/fopen): adding to final database dump
0029.068 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_revslider_static_slides_bkp.table.gz (98/84/fopen): adding to final database dump
0029.072 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_snippets.table.tmpr6.gz (99/84/fopen): adding to final database dump
0029.080 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_snippets.table.gz (100/84/fopen): adding to final database dump
0029.084 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_events.table.tmpr0.gz (101/84/fopen): adding to final database dump
0029.088 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_events.table.gz (102/84/fopen): adding to final database dump
0029.092 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_exclusions.table.tmpr0.gz (103/84/fopen): adding to final database dump
0029.097 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_exclusions.table.gz (104/84/fopen): adding to final database dump
0029.101 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_historical.table.tmpr0.gz (105/84/fopen): adding to final database dump
0029.105 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_historical.table.gz (106/84/fopen): adding to final database dump
0029.108 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_pages.table.tmpr5850.gz (107/84/fopen): adding to final database dump
0029.113 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_pages.table.gz (108/84/fopen): adding to final database dump
0029.116 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_search.table.tmpr1426.gz (109/84/fopen): adding to final database dump
0029.120 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_search.table.gz (110/84/fopen): adding to final database dump
0029.125 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_useronline.table.tmpr0.gz (111/84/fopen): adding to final database dump
0029.128 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_useronline.table.gz (112/84/fopen): adding to final database dump
0029.132 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_visit.table.tmpr2687.gz (113/84/fopen): adding to final database dump
0029.136 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_visit.table.gz (114/84/fopen): adding to final database dump
0029.141 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_visitor.table.tmpr9056.gz (115/84/fopen): adding to final database dump
0029.153 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_visitor.table.gz (116/84/fopen): adding to final database dump
0029.157 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_visitor_relationships.table.tmpr0.gz (117/84/fopen): adding to final database dump
0029.161 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_statistics_visitor_relationships.table.gz (118/84/fopen): adding to final database dump
0029.164 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_tm_taskmeta.table.tmpr2008.gz (119/84/fopen): adding to final database dump
0029.169 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_tm_taskmeta.table.gz (120/84/fopen): adding to final database dump
0029.172 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_tm_tasks.table.tmpr2008.gz (121/84/fopen): adding to final database dump
0029.176 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_tm_tasks.table.gz (122/84/fopen): adding to final database dump
0029.179 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_ru_ru_en_us.table.tmpr24732.gz (123/84/fopen): adding to final database dump
0029.205 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_ru_ru_en_us.table.gz (124/84/fopen): adding to final database dump
0029.209 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_ru_ru_tr_tr.table.tmpr24760.gz (125/84/fopen): adding to final database dump
0029.235 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_ru_ru_tr_tr.table.gz (126/84/fopen): adding to final database dump
0029.240 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_ru_ru_uk.table.tmpr24576.gz (127/84/fopen): adding to final database dump
0029.264 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_ru_ru_uk.table.gz (128/84/fopen): adding to final database dump
0029.269 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_de_de.table.tmpr3587.gz (129/84/fopen): adding to final database dump
0029.276 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_de_de.table.gz (130/84/fopen): adding to final database dump
0029.279 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_en_us.table.tmpr6501.gz (131/84/fopen): adding to final database dump
0029.288 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_en_us.table.gz (132/84/fopen): adding to final database dump
0029.292 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_ru_ru.table.tmpr6840.gz (133/84/fopen): adding to final database dump
0029.301 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_ru_ru.table.gz (134/84/fopen): adding to final database dump
0029.304 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_uk.table.tmpr6740.gz (135/84/fopen): adding to final database dump
0029.312 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_uk.table.gz (136/84/fopen): adding to final database dump
0029.316 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_zh_cn.table.tmpr11.gz (137/84/fopen): adding to final database dump
0029.321 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_dictionary_tr_tr_zh_cn.table.gz (138/84/fopen): adding to final database dump
0029.326 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_de_de.table.tmpr2279.gz (139/84/fopen): adding to final database dump
0029.331 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_de_de.table.gz (140/84/fopen): adding to final database dump
0029.336 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_en_us.table.tmpr3164.gz (141/84/fopen): adding to final database dump
0029.340 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_en_us.table.gz (142/84/fopen): adding to final database dump
0029.344 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_original_meta.table.tmpr0.gz (143/84/fopen): adding to final database dump
0029.347 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_original_meta.table.gz (144/84/fopen): adding to final database dump
0029.352 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_original_strings.table.tmpr3741.gz (145/84/fopen): adding to final database dump
0029.356 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_original_strings.table.gz (146/84/fopen): adding to final database dump
0029.359 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_ru_ru.table.tmpr3563.gz (147/84/fopen): adding to final database dump
0029.364 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_ru_ru.table.gz (148/84/fopen): adding to final database dump
0029.367 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_tr_tr.table.tmpr3122.gz (149/84/fopen): adding to final database dump
0029.373 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_tr_tr.table.gz (150/84/fopen): adding to final database dump
0029.376 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_uk.table.tmpr2658.gz (151/84/fopen): adding to final database dump
0029.381 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_uk.table.gz (152/84/fopen): adding to final database dump
0029.385 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_zh_cn.table.tmpr0.gz (153/84/fopen): adding to final database dump
0029.389 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_gettext_zh_cn.table.gz (154/84/fopen): adding to final database dump
0029.392 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_original_meta.table.tmpr45014.gz (155/84/fopen): adding to final database dump
0029.397 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_original_meta.table.gz (156/84/fopen): adding to final database dump
0029.400 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_original_strings.table.tmpr31227.gz (157/84/fopen): adding to final database dump
0029.420 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_trp_original_strings.table.gz (158/84/fopen): adding to final database dump
0029.423 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpfm_backup.table.tmpr0.gz (159/84/fopen): adding to final database dump
0029.427 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpfm_backup.table.gz (160/84/fopen): adding to final database dump
0029.430 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpforms_logs.table.tmpr0.gz (161/84/fopen): adding to final database dump
0029.434 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpforms_logs.table.gz (162/84/fopen): adding to final database dump
0029.437 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpforms_payment_meta.table.tmpr0.gz (163/84/fopen): adding to final database dump
0029.441 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpforms_payment_meta.table.gz (164/84/fopen): adding to final database dump
0029.443 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpforms_payments.table.tmpr0.gz (165/84/fopen): adding to final database dump
0029.446 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpforms_payments.table.gz (166/84/fopen): adding to final database dump
0029.449 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpforms_tasks_meta.table.tmpr55.gz (167/84/fopen): adding to final database dump
0029.453 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpforms_tasks_meta.table.gz (168/84/fopen): adding to final database dump
0029.456 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpo_404_detector.table.tmpr0.gz (169/84/fopen): adding to final database dump
0029.460 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db-table-wp_wpo_404_detector.table.gz (170/84/fopen): adding to final database dump
0029.475 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db.gz: finished writing out complete database file (38814.3 KB)
0029.809 (0) Total database tables backed up: 84 (backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db.gz, size: 39745983, sha1: a3f65e08751f0ef49422831825801dab39c183e7, sha256: 37015dbb9ebb16d67b8257ac9dd133e1ad5e46786df0cc1cf58e071ad1d9c36d)
0029.870 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes.zip: themes: This file has already been successfully processed
0029.874 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes2.zip: themes: This file has already been successfully processed
0029.878 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes3.zip: themes: This file has already been successfully processed
0029.882 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes4.zip: themes: This file has already been successfully processed
0029.886 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes5.zip: themes: This file has already been successfully processed
0029.888 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-themes6.zip: themes: This file has already been successfully processed
0029.892 (0) backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db.gz: db: This file has not yet been successfully uploaded: will queue
0029.895 (0) Saving backup history. Total backup size: 181.9 MB
0029.923 (0) Requesting upload of the files that have not yet been successfully uploaded (1)
0029.940 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0029.944 (0) No remote despatch: user chose no remote backup service
0029.947 (0) Recording as successfully uploaded: backup_2025-08-02-2150_Uptrend_Homes_0921d0dbfac7-db.gz
0029.956 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0029.960 (0) Resume backup (0921d0dbfac7, 0): finish run
0029.971 (0) Decremented the semaphore (fd) by 1
0029.977 (0) Semaphore (fd) unlocked
0029.980 (0) There were no errors in the uploads, so the 'resume' event (1) is being unscheduled
0029.995 (0) No email will/can be sent - the user has not configured an email address.
0029.999 (0) The backup succeeded and is now complete
