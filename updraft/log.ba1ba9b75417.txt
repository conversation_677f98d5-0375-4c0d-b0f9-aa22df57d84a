0000.002 (0) Opened log file at time: Sat, 26 Jul 2025 18:51:15 +0000 on http://uptrend.site
0000.008 (0) UpdraftPlus WordPress backup plugin (https://updraftplus.com): 1.25.6 WP: 6.8.2 PHP: 8.1.32 (a<PERSON><PERSON><PERSON><PERSON><PERSON>, Linux 9c1d7854d678 6.14.0-24-generic #24-Ubuntu SMP PREEMPT_DYNAMIC Sun Jun 15 11:18:07 UTC 2025 x86_64) MySQL: 8.4.4 (max packet size=67108864) WPLANG: ru_RU Server: Apache/2.4.62 (Debian) safe_mode: 0 max_execution_time: 900 memory_limit: 1G (used: 15.3M | 20M) multisite: N openssl: OpenSSL 3.0.15 3 Sep 2024 mcrypt: N LANG: C WP Proxy: disabled ZipArchive::addFile: Y
0000.011 (0) Free space on disk containing Updraft's temporary directory: 370001.9 MB
0000.020 (0) Tasks: Backup files: 1 (schedule: every4hours) Backup DB:  (schedule: every4hours)
0000.023 (0) Processed schedules. Combining jobs from identical schedules. Tasks now: Backup files: 1 Backup DB: 1
0000.030 (0) Requesting semaphore lock (fd) (apparently via scheduler: last_scheduled_action_called_at=1753541448, seconds_ago=14427)
0000.041 (0) Set semaphore last lock (fd) time to 2025-07-26 18:51:15
0000.044 (0) Semaphore lock (fd) complete
0000.054 (0) Backup run: resumption=0, nonce=ba1ba9b75417, file_nonce=ba1ba9b75417 begun at=1753555875 (0s ago), job type=backup
0000.061 (0) Scheduling a resumption (1) after 300 seconds (1753556175) in case this run gets aborted
0000.072 (0) Checking if we have a zip executable available
0000.079 (0) Creation of backups of directories: beginning
0000.087 (0) No backup of plugins: excluded by user's options
0000.095 (0) Beginning creation of dump of themes (split every: 25 MB)
0000.190 (0) Total entities for the zip file: 213 directories, 1773 files (0 skipped as non-modified), 170.6 MB
0000.198 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (26.6 MB, 1773 files batched, 49 (49) added so far); re-opening (prior size: 0 KB)
0000.519 (0) A useful amount of data was added after this amount of zip processing: 0.7 s (normalised: 0.6 s, rate: 41511.4 KB/s)
0000.522 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=0.65600395202637, normalised_time=0.61669748585165, max_time=0.5201530456543, data points known=1, max_bytes=26214400)
0000.529 (0) Zip size is at/near split limit (26.5 MB / 25 MB) - bumping index (from: 0)
0000.748 (0) Creating zip file manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes.zip.list-temp.tmp)
0000.753 (0) Successfully created zip file manifest (size: 3795)
0000.762 (0) Created themes zip (0) - 27096.9 KB in 0.4 s (62991.5 KB/s) (checksums: sha1: c12a7032ea444c61d7459614f65382b4057a40c3, sha256: 5417104913c1a68691a140fd0e0257276ed1256ac00ec179e30c2a61fe2c728f)
0000.780 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0000.784 (0) No remote despatch: user chose no remote backup service
0000.858 (0) Recording as successfully uploaded: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes.zip
0000.867 (0) Deleting zip manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes.zip.list.tmp)
0000.871 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0000.880 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (31.5 MB, 1773 files batched, 22 (71) added so far); re-opening (prior size: 0 KB)
0001.898 (0) A useful amount of data was added after this amount of zip processing: 2 s (normalised: 1.6 s, rate: 15875 KB/s)
0001.901 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=2.0350859165192, normalised_time=1.6125983598267, max_time=1.8989770412445, data points known=1, max_bytes=26214400)
0001.907 (0) Zip size is at/near split limit (31.6 MB / 25 MB) - bumping index (from: 1)
0002.161 (0) Creating zip file manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes2.zip.list-temp.tmp)
0002.165 (0) Successfully created zip file manifest (size: 2270)
0002.174 (0) Created themes zip (1) - 32313.6 KB in 1.1 s (28173.4 KB/s) (checksums: sha1: abc8b7081621fba45929380133a585413de360a2, sha256: a8811136b07fa86cba3e20e454a97775eba97cf8b774a7a89b60a13ca27ed02b)
0002.197 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0002.200 (0) No remote despatch: user chose no remote backup service
0002.203 (0) Recording as successfully uploaded: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes2.zip
0002.209 (0) Deleting zip manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes2.zip.list.tmp)
0002.215 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0002.223 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes3.zip.tmp: 100 files added (on-disk size: 0 KB)
0002.230 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (28 MB, 1773 files batched, 80 (151) added so far); re-opening (prior size: 0 KB)
0002.873 (0) A useful amount of data was added after this amount of zip processing: 1 s (normalised: 0.9 s, rate: 28416.1 KB/s)
0002.876 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=1.0102939605713, normalised_time=0.90089920008022, max_time=2.8742949962616, data points known=1, max_bytes=26214400)
0002.883 (0) Zip size is at/near split limit (28.1 MB / 25 MB) - bumping index (from: 2)
0003.116 (0) Creating zip file manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes3.zip.list-temp.tmp)
0003.120 (0) Successfully created zip file manifest (size: 7716)
0003.128 (0) Created themes zip (2) - 28728 KB in 0.7 s (40570.1 KB/s) (checksums: sha1: 38a4ea492d4ac60e91587cdde96b80f5d1c69541, sha256: 4d530de7eaa156e5c62a71b8d2f7ac048c48e302f9724605d28862bf911c6c51)
0003.149 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0003.152 (0) No remote despatch: user chose no remote backup service
0003.156 (0) Recording as successfully uploaded: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes3.zip
0003.162 (0) Deleting zip manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes3.zip.list.tmp)
0003.165 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0003.176 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes4.zip.tmp: 200 files added (on-disk size: 0 KB)
0003.183 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (25 MB, 1773 files batched, 102 (253) added so far); re-opening (prior size: 0 KB)
0003.393 (0) A useful amount of data was added after this amount of zip processing: 0.5 s (normalised: 0.5 s, rate: 48388.2 KB/s)
0003.396 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=0.52975797653198, normalised_time=0.52905445826109, max_time=3.3943030834198, data points known=1, max_bytes=26214400)
0003.405 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): possibly approaching split limit (0.2 MB, 1 (254) files added so far); last ratio: 0.9974; re-opening (prior size: 25566.7 KB)
0003.514 (0) Zip size is at/near split limit (25.2 MB / 25 MB) - bumping index (from: 3)
0003.718 (0) Creating zip file manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes4.zip.list-temp.tmp)
0003.722 (0) Successfully created zip file manifest (size: 7668)
0003.729 (0) Created themes zip (3) - 25810.6 KB in 0.4 s (66885.7 KB/s) (checksums: sha1: 23d2de251a31b0c5d7068ed92f4aa8d0284a578f, sha256: 19812a5776a129af6bcb2da1e50e0b266d9128db20f7a1e9ce9b7b5873d6f4ae)
0003.824 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0003.827 (0) No remote despatch: user chose no remote backup service
0003.831 (0) Recording as successfully uploaded: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes4.zip
0003.838 (0) Deleting zip manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes4.zip.list.tmp)
0003.842 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0003.852 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 300 files added (on-disk size: 0 KB)
0003.862 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 400 files added (on-disk size: 0 KB)
0003.870 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 500 files added (on-disk size: 0 KB)
0003.878 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 600 files added (on-disk size: 0 KB)
0003.885 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 700 files added (on-disk size: 0 KB)
0003.894 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 800 files added (on-disk size: 0 KB)
0003.902 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 900 files added (on-disk size: 0 KB)
0003.910 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 1000 files added (on-disk size: 0 KB)
0003.914 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (25 MB, 1773 files batched, 753 (1007) added so far); re-opening (prior size: 0 KB)
0005.660 (0) A useful amount of data was added after this amount of zip processing: 2.8 s (normalised: 2.8 s, rate: 9154.8 KB/s)
0005.663 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=2.7965679168701, normalised_time=2.7963595851795, max_time=5.6609120368958, data points known=1, max_bytes=26214400)
0005.686 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 1100 files added (on-disk size: 11397 KB)
0005.696 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 1200 files added (on-disk size: 11397 KB)
0005.705 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 1300 files added (on-disk size: 11397 KB)
0005.716 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 1400 files added (on-disk size: 11397 KB)
0005.725 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.tmp: 1500 files added (on-disk size: 11397 KB)
0005.731 (0) Adding batch to zip file (UpdraftPlus_ZipArchive): over 25 MB added on this batch (28.9 MB, 1773 files batched, 538 (1545) added so far); re-opening (prior size: 11397 KB)
0007.090 (0) A useful amount of data was added after this amount of zip processing: 2.2 s (normalised: 1.9 s, rate: 13278.2 KB/s)
0007.097 (0) Performance is good - but we will not increase the amount of data we batch, as we are already at the present limit (time=2.2266399860382, normalised_time=1.9279694627901, max_time=7.0944571495056, data points known=1, max_bytes=26214400)
0007.109 (0) Zip size is at/near split limit (31.3 MB / 25 MB) - bumping index (from: 4)
0007.371 (0) Creating zip file manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.list-temp.tmp)
0007.382 (0) Successfully created zip file manifest (size: 89504)
0007.389 (0) Created themes zip (4) - 32031.7 KB in 3.4 s (9476.7 KB/s) (checksums: sha1: 4521ccc86eb9ded0c616bdbb17dc76dec37545fd, sha256: 5fd5e3b6945c6458ab94e2ce44721f8b46a08bad450b134664d5d4da0375115d)
0007.413 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0007.416 (0) No remote despatch: user chose no remote backup service
0007.419 (0) Recording as successfully uploaded: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip
0007.427 (0) Deleting zip manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip.list.tmp)
0007.431 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0007.511 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes6.zip.tmp: 1600 files added (on-disk size: 0 KB)
0007.519 (0) Zip: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes6.zip.tmp: 1700 files added (on-disk size: 0 KB)
0007.912 (0) Creating zip file manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes6.zip.list-temp.tmp)
0007.917 (0) Successfully created zip file manifest (size: 13293)
0007.945 (0) Created themes zip (5) - 1425.7 KB in 0.5 s (2606.5 KB/s) (sha1: 70148f9ba661511d3f37c7f981d3bdc499a08181, sha256: 76a44891d7d7e2daf93e202416690e491d02499a4921c4c0a90a152599bb52b7)
0007.961 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0007.965 (0) No remote despatch: user chose no remote backup service
0007.968 (0) Recording as successfully uploaded: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes6.zip
0007.976 (0) Deleting zip manifest (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes6.zip.list.tmp)
0007.980 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0007.990 (0) No backup of uploads: excluded by user's options
0007.993 (0) No backup of mu-plugins: excluded by user's options
0007.996 (0) No backup of others: excluded by user's options
0008.013 (0) Saving backup status to database (elements: 7)
0008.038 (0) Beginning creation of database dump (WordPress DB)
0008.043 (0) SQL compatibility mode is: NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
0008.088 (0) Table wp_options: Total expected rows (approximate): 1063
0008.200 (0) Table wp_options: Rows added in this batch (next record: 508535): 1049 (uncompressed bytes in this segment=2673551) in 0.13 seconds
0008.219 (0) Table wp_options: finishing file(s) (2, 592.6 KB)
0008.232 (0) Table wp_users: Total expected rows (via COUNT): 3
0008.236 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.245 (0) Table wp_users: Rows added in this batch (next record: 16): 3 (uncompressed bytes in this segment=1688) in 0.02 seconds
0008.326 (0) Table wp_users: finishing file(s) (2, 0.7 KB)
0008.341 (0) Table wp_usermeta: Total expected rows (via COUNT): 203
0008.345 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.355 (0) Table wp_usermeta: Rows added in this batch (next record: 865): 203 (uncompressed bytes in this segment=18178) in 0.02 seconds
0008.371 (0) Table wp_usermeta: finishing file(s) (2, 4.6 KB)
0008.386 (0) Table wp_actionscheduler_actions: Total expected rows (via COUNT): 20
0008.389 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.397 (0) Table wp_actionscheduler_actions: Rows added in this batch (next record: 43159): 20 (uncompressed bytes in this segment=9244) in 0.02 seconds
0008.412 (0) Table wp_actionscheduler_actions: finishing file(s) (2, 1.6 KB)
0008.421 (0) Table wp_actionscheduler_claims: Total expected rows (via COUNT): 0
0008.428 (0) Table wp_actionscheduler_claims: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=633) in 0.01 seconds
0008.441 (0) Table wp_actionscheduler_claims: finishing file(s) (2, 0.4 KB)
0008.451 (0) Table wp_actionscheduler_groups: Total expected rows (via COUNT): 3
0008.454 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.462 (0) Table wp_actionscheduler_groups: Rows added in this batch (next record: 3): 3 (uncompressed bytes in this segment=718) in 0.01 seconds
0008.474 (0) Table wp_actionscheduler_groups: finishing file(s) (2, 0.4 KB)
0008.486 (0) Table wp_actionscheduler_logs: Total expected rows (via COUNT): 56
0008.489 (0) Table is relatively small; fetch_rows will thus be: 100 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.499 (0) Table wp_actionscheduler_logs: Rows added in this batch (next record: 127807): 56 (uncompressed bytes in this segment=8108) in 0.01 seconds
0008.512 (0) Table wp_actionscheduler_logs: finishing file(s) (2, 1.5 KB)
0008.528 (0) Table wp_commentmeta: Total expected rows (via COUNT): 3
0008.531 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.540 (0) Table wp_commentmeta: Rows added in this batch (next record: 32): 3 (uncompressed bytes in this segment=869) in 0.02 seconds
0008.557 (0) Table wp_commentmeta: finishing file(s) (2, 0.5 KB)
0008.571 (0) Table wp_comments: Total expected rows (via COUNT): 1
0008.574 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0008.579 (0) Table wp_comments: Rows added in this batch (next record: 9): 1 (uncompressed bytes in this segment=2339) in 0.01 seconds
0008.661 (0) Table wp_comments: finishing file(s) (2, 0.9 KB)
0008.674 (0) Table wp_links: Total expected rows (via COUNT): 0
0008.684 (0) Table wp_links: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1298) in 0.01 seconds
0008.696 (0) Table wp_links: finishing file(s) (2, 0.5 KB)
0009.202 (0) Table wp_postmeta: Total expected rows (approximate): 70505
0012.886 (0) Table wp_postmeta: Rows added in this batch (next record: 77314): 48000 (uncompressed bytes in this segment=118528591) in 4.18 seconds
0012.935 (0) Table wp_postmeta: Total expected rows (via COUNT): 94861
0013.713 (0) Table wp_postmeta: Rows added in this batch (next record: 125683): 46861 (uncompressed bytes in this segment=18279144) in 0.81 seconds
0013.731 (0) Table wp_postmeta: finishing file(s) (3, 13995.5 KB)
0014.840 (0) Table wp_posts: Total expected rows (approximate): 5676
0018.253 (0) Table wp_posts: Rows added in this batch (next record: 35205): 2603 (uncompressed bytes in this segment=107282324) in 4.51 seconds
0018.273 (0) Table wp_posts: Total expected rows (via COUNT): 6101
0018.689 (0) Table wp_posts: Rows added in this batch (next record: 38895): 3498 (uncompressed bytes in this segment=8540289) in 0.42 seconds
0018.779 (0) Table wp_posts: finishing file(s) (3, 14984.6 KB)
0018.862 (0) Table wp_term_relationships: Total expected rows (approximate): 3175
0018.889 (0) Table wp_term_relationships: Rows added in this batch (next record: 100000): 3334 (uncompressed bytes in this segment=60373) in 0.03 seconds
0018.903 (0) Table wp_term_relationships: finishing file(s) (2, 9.4 KB)
0018.918 (0) Table wp_term_taxonomy: Total expected rows (via COUNT): 238
0018.921 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0018.931 (0) Table wp_term_taxonomy: Rows added in this batch (next record: 314): 238 (uncompressed bytes in this segment=13690) in 0.02 seconds
0018.947 (0) Table wp_term_taxonomy: finishing file(s) (2, 3.7 KB)
0018.961 (0) Table wp_termmeta: Total expected rows (via COUNT): 0
0018.968 (0) Table wp_termmeta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=650) in 0.01 seconds
0018.985 (0) Table wp_termmeta: finishing file(s) (2, 0.4 KB)
0018.995 (0) Table wp_terms: Total expected rows (via COUNT): 238
0018.998 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0019.007 (0) Table wp_terms: Rows added in this batch (next record: 314): 238 (uncompressed bytes in this segment=16255) in 0.01 seconds
0019.019 (0) Table wp_terms: finishing file(s) (2, 4.6 KB)
0019.033 (0) Table wp_aioseo_cache: Total expected rows (via COUNT): 14
0019.036 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0019.045 (0) Table wp_aioseo_cache: Rows added in this batch (next record: 14872): 14 (uncompressed bytes in this segment=98318) in 0.02 seconds
0019.061 (0) Table wp_aioseo_cache: finishing file(s) (2, 19 KB)
0019.071 (0) Table wp_aioseo_crawl_cleanup_blocked_args: Total expected rows (via COUNT): 0
0019.079 (0) Table wp_aioseo_crawl_cleanup_blocked_args: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1039) in 0.01 seconds
0019.091 (0) Table wp_aioseo_crawl_cleanup_blocked_args: finishing file(s) (2, 0.4 KB)
0019.102 (0) Table wp_aioseo_crawl_cleanup_logs: Total expected rows (via COUNT): 0
0019.107 (0) Table wp_aioseo_crawl_cleanup_logs: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=875) in 0.01 seconds
0019.120 (0) Table wp_aioseo_crawl_cleanup_logs: finishing file(s) (2, 0.4 KB)
0019.137 (0) Table wp_aioseo_notifications: Total expected rows (via COUNT): 39
0019.140 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0019.159 (0) Table wp_aioseo_notifications: Rows added in this batch (next record: 39): 39 (uncompressed bytes in this segment=272673) in 0.03 seconds
0019.178 (0) Table wp_aioseo_notifications: finishing file(s) (2, 187.1 KB)
0019.198 (0) Table wp_aioseo_posts: Total expected rows (approximate): 4191
0019.702 (0) Table wp_aioseo_posts: Rows added in this batch (next record: 4918): 4835 (uncompressed bytes in this segment=8104745) in 0.52 seconds
0019.721 (0) Table wp_aioseo_posts: finishing file(s) (2, 114.1 KB)
0019.735 (0) Table wp_chaty_contact_form_leads: Total expected rows (via COUNT): 0
0019.738 (0) Table wp_chaty_contact_form_leads: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=958) in 0.01 seconds
0019.750 (0) Table wp_chaty_contact_form_leads: finishing file(s) (2, 0.4 KB)
0019.762 (0) Table wp_chaty_widget_analysis: Total expected rows (approximate): 2261
0019.798 (0) Table wp_chaty_widget_analysis: Rows added in this batch (next record: 2325): 2325 (uncompressed bytes in this segment=98832) in 0.04 seconds
0019.811 (0) Table wp_chaty_widget_analysis: finishing file(s) (2, 12.8 KB)
0019.829 (0) Table wp_db7_forms: Total expected rows (via COUNT): 0
0019.834 (0) Table wp_db7_forms: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=567) in 0.01 seconds
0019.850 (0) Table wp_db7_forms: finishing file(s) (2, 0.4 KB)
0019.863 (0) Table wp_e_events: Total expected rows (via COUNT): 1
0019.866 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0019.874 (0) Table wp_e_events: Rows added in this batch (next record: 1): 1 (uncompressed bytes in this segment=787) in 0.01 seconds
0019.887 (0) Table wp_e_events: finishing file(s) (2, 0.5 KB)
0019.900 (0) Table wp_e_submissions: Total expected rows (approximate): 6911
0020.277 (0) Table wp_e_submissions: Rows added in this batch (next record: 7127): 7127 (uncompressed bytes in this segment=3677283) in 0.38 seconds
0020.291 (0) Table wp_e_submissions: finishing file(s) (2, 385.8 KB)
0020.304 (0) Table wp_e_submissions_actions_log: Total expected rows (approximate): 6924
0020.510 (0) Table wp_e_submissions_actions_log: Rows added in this batch (next record: 7115): 7115 (uncompressed bytes in this segment=1261322) in 0.21 seconds
0020.596 (0) Table wp_e_submissions_actions_log: finishing file(s) (2, 119.6 KB)
0020.674 (0) Table wp_e_submissions_values: Total expected rows (approximate): 38837
0021.456 (0) Table wp_e_submissions_values: Rows added in this batch (next record: 35565): 35565 (uncompressed bytes in this segment=10380213) in 0.85 seconds
0021.480 (0) Table wp_e_submissions_values: finishing file(s) (2, 2699.2 KB)
0021.500 (0) Table wp_eacf7_draft_submissions: Total expected rows (via COUNT): 0
0021.503 (0) Table wp_eacf7_draft_submissions: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=907) in 0.01 seconds
0021.521 (0) Table wp_eacf7_draft_submissions: finishing file(s) (2, 0.4 KB)
0021.540 (0) Table wp_eacf7_entries: Total expected rows (via COUNT): 0
0021.544 (0) Table wp_eacf7_entries: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=754) in 0.01 seconds
0021.562 (0) Table wp_eacf7_entries: finishing file(s) (2, 0.4 KB)
0021.585 (0) Table wp_mystickyelement_contact_lists: Total expected rows (via COUNT): 2
0021.588 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.598 (0) Table wp_mystickyelement_contact_lists: Rows added in this batch (next record: 2): 2 (uncompressed bytes in this segment=1618) in 0.02 seconds
0021.614 (0) Table wp_mystickyelement_contact_lists: finishing file(s) (2, 0.7 KB)
0021.625 (0) Table wp_odb_logs: Total expected rows (via COUNT): 0
0021.632 (0) Table wp_odb_logs: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=782) in 0.01 seconds
0021.643 (0) Table wp_odb_logs: finishing file(s) (2, 0.4 KB)
0021.661 (0) Table wp_pmxe_exports: Total expected rows (via COUNT): 1
0021.664 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.674 (0) Table wp_pmxe_exports: Rows added in this batch (next record: 1): 1 (uncompressed bytes in this segment=84320) in 0.02 seconds
0021.689 (0) Table wp_pmxe_exports: finishing file(s) (2, 14.8 KB)
0021.702 (0) Table wp_pmxe_google_cats: Total expected rows (approximate): 5407
0021.790 (0) Table wp_pmxe_google_cats: Rows added in this batch (next record: 505832): 5371 (uncompressed bytes in this segment=327566) in 0.09 seconds
0021.803 (0) Table wp_pmxe_google_cats: finishing file(s) (2, 75.3 KB)
0021.817 (0) Table wp_pmxe_posts: Total expected rows (via COUNT): 144
0021.820 (0) Table is relatively small; fetch_rows will thus be: 250 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.830 (0) Table wp_pmxe_posts: Rows added in this batch (next record: 144): 144 (uncompressed bytes in this segment=3503) in 0.02 seconds
0021.843 (0) Table wp_pmxe_posts: finishing file(s) (2, 1.1 KB)
0021.862 (0) Table wp_pmxe_templates: Total expected rows (via COUNT): 0
0021.869 (0) Table wp_pmxe_templates: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=561) in 0.02 seconds
0021.886 (0) Table wp_pmxe_templates: finishing file(s) (2, 0.3 KB)
0021.905 (0) Table wp_revslider_css: Total expected rows (via COUNT): 109
0021.908 (0) Table is relatively small; fetch_rows will thus be: 200 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.919 (0) Table wp_revslider_css: Rows added in this batch (next record: 109): 109 (uncompressed bytes in this segment=91061) in 0.02 seconds
0021.938 (0) Table wp_revslider_css: finishing file(s) (2, 4.9 KB)
0021.956 (0) Table wp_revslider_css_bkp: Total expected rows (via COUNT): 47
0021.959 (0) Table is relatively small; fetch_rows will thus be: 50 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0021.968 (0) Table wp_revslider_css_bkp: Rows added in this batch (next record: 47): 47 (uncompressed bytes in this segment=15390) in 0.02 seconds
0021.985 (0) Table wp_revslider_css_bkp: finishing file(s) (2, 1.8 KB)
0021.999 (0) Table wp_revslider_layer_animations: Total expected rows (via COUNT): 0
0022.007 (0) Table wp_revslider_layer_animations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=533) in 0.01 seconds
0022.021 (0) Table wp_revslider_layer_animations: finishing file(s) (2, 0.3 KB)
0022.035 (0) Table wp_revslider_layer_animations_bkp: Total expected rows (via COUNT): 0
0022.040 (0) Table wp_revslider_layer_animations_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=557) in 0.01 seconds
0022.053 (0) Table wp_revslider_layer_animations_bkp: finishing file(s) (2, 0.3 KB)
0022.073 (0) Table wp_revslider_navigations: Total expected rows (via COUNT): 0
0022.077 (0) Table wp_revslider_navigations: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=610) in 0.01 seconds
0022.098 (0) Table wp_revslider_navigations: finishing file(s) (2, 0.3 KB)
0022.115 (0) Table wp_revslider_navigations_bkp: Total expected rows (via COUNT): 0
0022.119 (0) Table wp_revslider_navigations_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=634) in 0.01 seconds
0022.135 (0) Table wp_revslider_navigations_bkp: finishing file(s) (2, 0.3 KB)
0022.153 (0) Table wp_revslider_sliders: Total expected rows (via COUNT): 1
0022.156 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0022.165 (0) Table wp_revslider_sliders: Rows added in this batch (next record: 1): 1 (uncompressed bytes in this segment=9624) in 0.02 seconds
0022.182 (0) Table wp_revslider_sliders: finishing file(s) (2, 3.2 KB)
0022.198 (0) Table wp_revslider_sliders_bkp: Total expected rows (via COUNT): 0
0022.208 (0) Table wp_revslider_sliders_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=605) in 0.02 seconds
0022.226 (0) Table wp_revslider_sliders_bkp: finishing file(s) (2, 0.4 KB)
0022.244 (0) Table wp_revslider_slides: Total expected rows (via COUNT): 6
0022.247 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0022.256 (0) Table wp_revslider_slides: Rows added in this batch (next record: 6): 6 (uncompressed bytes in this segment=8227) in 0.02 seconds
0022.273 (0) Table wp_revslider_slides: finishing file(s) (2, 1 KB)
0022.290 (0) Table wp_revslider_slides_bkp: Total expected rows (via COUNT): 0
0022.298 (0) Table wp_revslider_slides_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=611) in 0.02 seconds
0022.313 (0) Table wp_revslider_slides_bkp: finishing file(s) (2, 0.3 KB)
0022.332 (0) Table wp_revslider_static_slides: Total expected rows (via COUNT): 1
0022.336 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0022.344 (0) Table wp_revslider_static_slides: Rows added in this batch (next record: 1): 1 (uncompressed bytes in this segment=700) in 0.02 seconds
0022.363 (0) Table wp_revslider_static_slides: finishing file(s) (2, 0.4 KB)
0022.386 (0) Table wp_revslider_static_slides_bkp: Total expected rows (via COUNT): 0
0022.395 (0) Table wp_revslider_static_slides_bkp: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=623) in 0.02 seconds
0022.411 (0) Table wp_revslider_static_slides_bkp: finishing file(s) (2, 0.3 KB)
0022.431 (0) Table wp_snippets: Total expected rows (via COUNT): 5
0022.434 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0022.441 (0) Table wp_snippets: Rows added in this batch (next record: 5): 5 (uncompressed bytes in this segment=3107) in 0.02 seconds
0022.454 (0) Table wp_snippets: finishing file(s) (2, 1.2 KB)
0022.466 (0) Table wp_statistics_events: Total expected rows (via COUNT): 0
0022.472 (0) Table wp_statistics_events: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=768) in 0.01 seconds
0022.483 (0) Table wp_statistics_events: finishing file(s) (2, 0.4 KB)
0022.494 (0) Table wp_statistics_exclusions: Total expected rows (via COUNT): 0
0022.498 (0) Table wp_statistics_exclusions: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=637) in 0.01 seconds
0022.509 (0) Table wp_statistics_exclusions: finishing file(s) (2, 0.4 KB)
0022.592 (0) Table wp_statistics_historical: Total expected rows (via COUNT): 0
0022.596 (0) Table wp_statistics_historical: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=710) in 0.08 seconds
0022.609 (0) Table wp_statistics_historical: finishing file(s) (2, 0.4 KB)
0022.622 (0) Table wp_statistics_pages: Total expected rows (approximate): 5824
0022.792 (0) Table wp_statistics_pages: Rows added in this batch (next record: 5849): 5832 (uncompressed bytes in this segment=579450) in 0.17 seconds
0022.807 (0) Table wp_statistics_pages: finishing file(s) (2, 66.8 KB)
0022.820 (0) Table wp_statistics_search: Total expected rows (approximate): 1425
0022.846 (0) Table wp_statistics_search: Rows added in this batch (next record: 1425): 1425 (uncompressed bytes in this segment=94902) in 0.03 seconds
0022.858 (0) Table wp_statistics_search: finishing file(s) (2, 12.2 KB)
0022.872 (0) Table wp_statistics_useronline: Total expected rows (via COUNT): 0
0022.876 (0) Table wp_statistics_useronline: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1125) in 0.01 seconds
0022.888 (0) Table wp_statistics_useronline: finishing file(s) (2, 0.5 KB)
0022.901 (0) Table wp_statistics_visit: Total expected rows (via COUNT): 612
0022.911 (0) Table wp_statistics_visit: Rows added in this batch (next record: 2686): 612 (uncompressed bytes in this segment=30865) in 0.01 seconds
0022.923 (0) Table wp_statistics_visit: finishing file(s) (2, 8.1 KB)
0022.937 (0) Table wp_statistics_visitor: Total expected rows (approximate): 8824
0023.246 (0) Table wp_statistics_visitor: Rows added in this batch (next record: 9055): 9041 (uncompressed bytes in this segment=1682962) in 0.31 seconds
0023.260 (0) Table wp_statistics_visitor: finishing file(s) (2, 341.3 KB)
0023.274 (0) Table wp_statistics_visitor_relationships: Total expected rows (via COUNT): 0
0023.277 (0) Table wp_statistics_visitor_relationships: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=686) in 0.01 seconds
0023.291 (0) Table wp_statistics_visitor_relationships: finishing file(s) (2, 0.4 KB)
0023.312 (0) Table wp_tm_taskmeta: Total expected rows (approximate): 1862
0023.344 (0) Table wp_tm_taskmeta: Rows added in this batch (next record: 1965): 1965 (uncompressed bytes in this segment=398718) in 0.04 seconds
0023.365 (0) Table wp_tm_taskmeta: finishing file(s) (2, 25.9 KB)
0023.377 (0) Table wp_tm_tasks: Total expected rows (approximate): 1812
0023.430 (0) Table wp_tm_tasks: Rows added in this batch (next record: 1965): 1965 (uncompressed bytes in this segment=372080) in 0.06 seconds
0023.441 (0) Table wp_tm_tasks: finishing file(s) (2, 17.6 KB)
0023.566 (0) Table wp_trp_dictionary_ru_ru_en_us: Total expected rows (approximate): 25425
0024.029 (0) Table wp_trp_dictionary_ru_ru_en_us: Rows added in this batch (next record: 24731): 24441 (uncompressed bytes in this segment=5173730) in 0.58 seconds
0024.046 (0) Table wp_trp_dictionary_ru_ru_en_us: finishing file(s) (2, 1014.3 KB)
0024.173 (0) Table wp_trp_dictionary_ru_ru_tr_tr: Total expected rows (approximate): 23957
0024.628 (0) Table wp_trp_dictionary_ru_ru_tr_tr: Rows added in this batch (next record: 24759): 24377 (uncompressed bytes in this segment=5021320) in 0.57 seconds
0024.650 (0) Table wp_trp_dictionary_ru_ru_tr_tr: finishing file(s) (2, 994.7 KB)
0024.699 (0) Table wp_trp_dictionary_ru_ru_uk: Total expected rows (approximate): 24700
0025.188 (0) Table wp_trp_dictionary_ru_ru_uk: Rows added in this batch (next record: 24575): 24328 (uncompressed bytes in this segment=5405708) in 0.53 seconds
0025.205 (0) Table wp_trp_dictionary_ru_ru_uk: finishing file(s) (2, 980.7 KB)
0025.231 (0) Table wp_trp_dictionary_tr_tr_de_de: Total expected rows (approximate): 3520
0025.294 (0) Table wp_trp_dictionary_tr_tr_de_de: Rows added in this batch (next record: 3586): 3586 (uncompressed bytes in this segment=409263) in 0.08 seconds
0025.311 (0) Table wp_trp_dictionary_tr_tr_de_de: finishing file(s) (2, 97.6 KB)
0025.340 (0) Table wp_trp_dictionary_tr_tr_en_us: Total expected rows (approximate): 6361
0025.458 (0) Table wp_trp_dictionary_tr_tr_en_us: Rows added in this batch (next record: 6500): 6500 (uncompressed bytes in this segment=836813) in 0.14 seconds
0025.475 (0) Table wp_trp_dictionary_tr_tr_en_us: finishing file(s) (2, 194.5 KB)
0025.578 (0) Table wp_trp_dictionary_tr_tr_ru_ru: Total expected rows (approximate): 7113
0025.701 (0) Table wp_trp_dictionary_tr_tr_ru_ru: Rows added in this batch (next record: 6839): 6839 (uncompressed bytes in this segment=912966) in 0.22 seconds
0025.719 (0) Table wp_trp_dictionary_tr_tr_ru_ru: finishing file(s) (2, 204.5 KB)
0025.750 (0) Table wp_trp_dictionary_tr_tr_uk: Total expected rows (approximate): 6357
0025.871 (0) Table wp_trp_dictionary_tr_tr_uk: Rows added in this batch (next record: 6739): 6739 (uncompressed bytes in this segment=905812) in 0.14 seconds
0025.890 (0) Table wp_trp_dictionary_tr_tr_uk: finishing file(s) (2, 204.5 KB)
0025.909 (0) Table wp_trp_dictionary_tr_tr_zh_cn: Total expected rows (via COUNT): 10
0025.912 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0025.920 (0) Table wp_trp_dictionary_tr_tr_zh_cn: Rows added in this batch (next record: 10): 10 (uncompressed bytes in this segment=3586) in 0.02 seconds
0025.935 (0) Table wp_trp_dictionary_tr_tr_zh_cn: finishing file(s) (2, 1.5 KB)
0025.956 (0) Table wp_trp_gettext_de_de: Total expected rows (approximate): 2278
0026.004 (0) Table wp_trp_gettext_de_de: Rows added in this batch (next record: 2278): 2278 (uncompressed bytes in this segment=161114) in 0.06 seconds
0026.021 (0) Table wp_trp_gettext_de_de: finishing file(s) (2, 40.1 KB)
0026.045 (0) Table wp_trp_gettext_en_us: Total expected rows (approximate): 3040
0026.105 (0) Table wp_trp_gettext_en_us: Rows added in this batch (next record: 3163): 3163 (uncompressed bytes in this segment=220817) in 0.08 seconds
0026.123 (0) Table wp_trp_gettext_en_us: finishing file(s) (2, 51.1 KB)
0026.146 (0) Table wp_trp_gettext_original_meta: Total expected rows (via COUNT): 0
0026.158 (0) Table wp_trp_gettext_original_meta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=787) in 0.02 seconds
0026.174 (0) Table wp_trp_gettext_original_meta: finishing file(s) (2, 0.4 KB)
0026.185 (0) Table wp_trp_gettext_original_strings: Total expected rows (approximate): 3459
0026.248 (0) Table wp_trp_gettext_original_strings: Rows added in this batch (next record: 3724): 3666 (uncompressed bytes in this segment=256787) in 0.07 seconds
0026.262 (0) Table wp_trp_gettext_original_strings: finishing file(s) (2, 46.7 KB)
0026.282 (0) Table wp_trp_gettext_ru_ru: Total expected rows (approximate): 3324
0026.357 (0) Table wp_trp_gettext_ru_ru: Rows added in this batch (next record: 3542): 3542 (uncompressed bytes in this segment=385853) in 0.08 seconds
0026.375 (0) Table wp_trp_gettext_ru_ru: finishing file(s) (2, 97.7 KB)
0026.402 (0) Table wp_trp_gettext_tr_tr: Total expected rows (approximate): 3036
0026.464 (0) Table wp_trp_gettext_tr_tr: Rows added in this batch (next record: 3121): 3121 (uncompressed bytes in this segment=270901) in 0.08 seconds
0026.485 (0) Table wp_trp_gettext_tr_tr: finishing file(s) (2, 76.1 KB)
0026.511 (0) Table wp_trp_gettext_uk: Total expected rows (approximate): 2482
0026.570 (0) Table wp_trp_gettext_uk: Rows added in this batch (next record: 2657): 2657 (uncompressed bytes in this segment=283109) in 0.07 seconds
0026.591 (0) Table wp_trp_gettext_uk: finishing file(s) (2, 72.5 KB)
0026.610 (0) Table wp_trp_gettext_zh_cn: Total expected rows (via COUNT): 0
0026.614 (0) Table wp_trp_gettext_zh_cn: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=827) in 0.01 seconds
0026.633 (0) Table wp_trp_gettext_zh_cn: finishing file(s) (2, 0.4 KB)
0026.697 (0) Table wp_trp_original_meta: Total expected rows (approximate): 45040
0027.228 (0) Table wp_trp_original_meta: Rows added in this batch (next record: 45013): 45013 (uncompressed bytes in this segment=1936587) in 0.59 seconds
0027.248 (0) Table wp_trp_original_meta: finishing file(s) (2, 206.2 KB)
0027.263 (0) Table wp_trp_original_strings: Total expected rows (approximate): 35966
0027.658 (0) Table wp_trp_original_strings: Rows added in this batch (next record: 31226): 31226 (uncompressed bytes in this segment=5229954) in 0.40 seconds
0027.673 (0) Table wp_trp_original_strings: finishing file(s) (2, 787 KB)
0027.686 (0) Table wp_wpfm_backup: Total expected rows (via COUNT): 0
0027.690 (0) Table wp_wpfm_backup: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=510) in 0.01 seconds
0027.702 (0) Table wp_wpfm_backup: finishing file(s) (2, 0.3 KB)
0027.720 (0) Table wp_wpforms_logs: Total expected rows (via COUNT): 0
0027.723 (0) Table wp_wpforms_logs: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=736) in 0.01 seconds
0027.739 (0) Table wp_wpforms_logs: finishing file(s) (2, 0.4 KB)
0027.756 (0) Table wp_wpforms_payment_meta: Total expected rows (via COUNT): 0
0027.764 (0) Table wp_wpforms_payment_meta: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=731) in 0.02 seconds
0027.782 (0) Table wp_wpforms_payment_meta: finishing file(s) (2, 0.4 KB)
0027.798 (0) Table wp_wpforms_payments: Total expected rows (via COUNT): 0
0027.807 (0) Table wp_wpforms_payments: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=1958) in 0.02 seconds
0027.821 (0) Table wp_wpforms_payments: finishing file(s) (2, 0.6 KB)
0027.838 (0) Table wp_wpforms_tasks_meta: Total expected rows (via COUNT): 3
0027.842 (0) Table is relatively small; fetch_rows will thus be: 20 (allow_further_reductions=1, is_first_fetch=1, known_bigger_than_table=1)
0027.850 (0) Table wp_wpforms_tasks_meta: Rows added in this batch (next record: 54): 3 (uncompressed bytes in this segment=898) in 0.02 seconds
0027.870 (0) Table wp_wpforms_tasks_meta: finishing file(s) (2, 0.5 KB)
0027.884 (0) Table wp_wpo_404_detector: Total expected rows (via COUNT): 0
0027.893 (0) Table wp_wpo_404_detector: Rows added in this batch (next record: -1): 0 (uncompressed bytes in this segment=857) in 0.01 seconds
0027.909 (0) Table wp_wpo_404_detector: finishing file(s) (2, 0.4 KB)
0027.912 (0) PHP event: code E_WARNING: filemtime(): stat failed for /var/www/html/wp-content/updraft/backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db.gz (line 1923, wp-content/plugins/updraftplus/backup.php)
0027.918 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_options.table.tmpr508536.gz (1/84/fopen): adding to final database dump
0027.943 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_options.table.gz (2/84/fopen): adding to final database dump
0027.947 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_users.table.tmpr17.gz (3/84/fopen): adding to final database dump
0027.951 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_users.table.gz (4/84/fopen): adding to final database dump
0027.954 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_usermeta.table.tmpr866.gz (5/84/fopen): adding to final database dump
0027.958 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_usermeta.table.gz (6/84/fopen): adding to final database dump
0027.963 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_actionscheduler_actions.table.tmpr43160.gz (7/84/fopen): adding to final database dump
0027.966 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_actionscheduler_actions.table.gz (8/84/fopen): adding to final database dump
0027.970 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_actionscheduler_claims.table.tmpr0.gz (9/84/fopen): adding to final database dump
0027.973 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_actionscheduler_claims.table.gz (10/84/fopen): adding to final database dump
0027.977 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_actionscheduler_groups.table.tmpr4.gz (11/84/fopen): adding to final database dump
0027.980 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_actionscheduler_groups.table.gz (12/84/fopen): adding to final database dump
0027.984 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_actionscheduler_logs.table.tmpr127808.gz (13/84/fopen): adding to final database dump
0027.987 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_actionscheduler_logs.table.gz (14/84/fopen): adding to final database dump
0027.992 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_commentmeta.table.tmpr33.gz (15/84/fopen): adding to final database dump
0027.996 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_commentmeta.table.gz (16/84/fopen): adding to final database dump
0028.000 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_comments.table.tmpr10.gz (17/84/fopen): adding to final database dump
0028.003 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_comments.table.gz (18/84/fopen): adding to final database dump
0028.007 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_links.table.tmpr0.gz (19/84/fopen): adding to final database dump
0028.011 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_links.table.gz (20/84/fopen): adding to final database dump
0028.014 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_postmeta.table.tmpr77314.gz (21/84/fopen): adding to final database dump
0028.351 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_postmeta.table.tmpr125684.gz (22/84/fopen): adding to final database dump
0028.419 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_postmeta.table.gz (23/84/fopen): adding to final database dump
0028.423 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_posts.table.tmpr35205.gz (24/84/fopen): adding to final database dump
0028.789 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_posts.table.tmpr38896.gz (25/84/fopen): adding to final database dump
0028.832 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_posts.table.gz (26/84/fopen): adding to final database dump
0028.836 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_term_relationships.table.tmpr100001.gz (27/84/fopen): adding to final database dump
0028.840 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_term_relationships.table.gz (28/84/fopen): adding to final database dump
0028.843 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_term_taxonomy.table.tmpr315.gz (29/84/fopen): adding to final database dump
0028.846 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_term_taxonomy.table.gz (30/84/fopen): adding to final database dump
0028.849 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_termmeta.table.tmpr0.gz (31/84/fopen): adding to final database dump
0028.853 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_termmeta.table.gz (32/84/fopen): adding to final database dump
0028.856 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_terms.table.tmpr315.gz (33/84/fopen): adding to final database dump
0028.859 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_terms.table.gz (34/84/fopen): adding to final database dump
0028.863 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_cache.table.tmpr14873.gz (35/84/fopen): adding to final database dump
0028.867 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_cache.table.gz (36/84/fopen): adding to final database dump
0028.870 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_crawl_cleanup_blocked_args.table.tmpr0.gz (37/84/fopen): adding to final database dump
0028.874 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_crawl_cleanup_blocked_args.table.gz (38/84/fopen): adding to final database dump
0028.877 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_crawl_cleanup_logs.table.tmpr0.gz (39/84/fopen): adding to final database dump
0028.880 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_crawl_cleanup_logs.table.gz (40/84/fopen): adding to final database dump
0028.883 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_notifications.table.tmpr40.gz (41/84/fopen): adding to final database dump
0028.891 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_notifications.table.gz (42/84/fopen): adding to final database dump
0028.896 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_posts.table.tmpr4919.gz (43/84/fopen): adding to final database dump
0028.904 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_aioseo_posts.table.gz (44/84/fopen): adding to final database dump
0028.907 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_chaty_contact_form_leads.table.tmpr0.gz (45/84/fopen): adding to final database dump
0028.910 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_chaty_contact_form_leads.table.gz (46/84/fopen): adding to final database dump
0028.913 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_chaty_widget_analysis.table.tmpr2326.gz (47/84/fopen): adding to final database dump
0028.916 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_chaty_widget_analysis.table.gz (48/84/fopen): adding to final database dump
0028.919 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_db7_forms.table.tmpr0.gz (49/84/fopen): adding to final database dump
0028.922 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_db7_forms.table.gz (50/84/fopen): adding to final database dump
0028.924 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_e_events.table.tmpr2.gz (51/84/fopen): adding to final database dump
0028.929 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_e_events.table.gz (52/84/fopen): adding to final database dump
0028.932 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_e_submissions.table.tmpr7128.gz (53/84/fopen): adding to final database dump
0028.948 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_e_submissions.table.gz (54/84/fopen): adding to final database dump
0028.951 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_e_submissions_actions_log.table.tmpr7116.gz (55/84/fopen): adding to final database dump
0028.958 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_e_submissions_actions_log.table.gz (56/84/fopen): adding to final database dump
0028.961 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_e_submissions_values.table.tmpr35566.gz (57/84/fopen): adding to final database dump
0029.049 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_e_submissions_values.table.gz (58/84/fopen): adding to final database dump
0029.123 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_eacf7_draft_submissions.table.tmpr0.gz (59/84/fopen): adding to final database dump
0029.128 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_eacf7_draft_submissions.table.gz (60/84/fopen): adding to final database dump
0029.204 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_eacf7_entries.table.tmpr0.gz (61/84/fopen): adding to final database dump
0029.208 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_eacf7_entries.table.gz (62/84/fopen): adding to final database dump
0029.212 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_mystickyelement_contact_lists.table.tmpr3.gz (63/84/fopen): adding to final database dump
0029.287 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_mystickyelement_contact_lists.table.gz (64/84/fopen): adding to final database dump
0029.291 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_odb_logs.table.tmpr0.gz (65/84/fopen): adding to final database dump
0029.366 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_odb_logs.table.gz (66/84/fopen): adding to final database dump
0029.370 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_pmxe_exports.table.tmpr2.gz (67/84/fopen): adding to final database dump
0029.375 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_pmxe_exports.table.gz (68/84/fopen): adding to final database dump
0029.379 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_pmxe_google_cats.table.tmpr505833.gz (69/84/fopen): adding to final database dump
0029.456 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_pmxe_google_cats.table.gz (70/84/fopen): adding to final database dump
0029.531 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_pmxe_posts.table.tmpr145.gz (71/84/fopen): adding to final database dump
0029.606 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_pmxe_posts.table.gz (72/84/fopen): adding to final database dump
0029.609 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_pmxe_templates.table.tmpr0.gz (73/84/fopen): adding to final database dump
0029.613 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_pmxe_templates.table.gz (74/84/fopen): adding to final database dump
0029.688 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_css.table.tmpr110.gz (75/84/fopen): adding to final database dump
0029.691 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_css.table.gz (76/84/fopen): adding to final database dump
0029.694 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_css_bkp.table.tmpr48.gz (77/84/fopen): adding to final database dump
0029.698 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_css_bkp.table.gz (78/84/fopen): adding to final database dump
0029.702 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_layer_animations.table.tmpr0.gz (79/84/fopen): adding to final database dump
0029.705 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_layer_animations.table.gz (80/84/fopen): adding to final database dump
0029.708 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_layer_animations_bkp.table.tmpr0.gz (81/84/fopen): adding to final database dump
0029.711 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_layer_animations_bkp.table.gz (82/84/fopen): adding to final database dump
0029.713 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_navigations.table.tmpr0.gz (83/84/fopen): adding to final database dump
0029.787 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_navigations.table.gz (84/84/fopen): adding to final database dump
0029.791 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_navigations_bkp.table.tmpr0.gz (85/84/fopen): adding to final database dump
0029.865 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_navigations_bkp.table.gz (86/84/fopen): adding to final database dump
0029.870 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_sliders.table.tmpr2.gz (87/84/fopen): adding to final database dump
0029.946 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_sliders.table.gz (88/84/fopen): adding to final database dump
0029.950 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_sliders_bkp.table.tmpr0.gz (89/84/fopen): adding to final database dump
0029.954 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_sliders_bkp.table.gz (90/84/fopen): adding to final database dump
0030.029 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_slides.table.tmpr7.gz (91/84/fopen): adding to final database dump
0030.033 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_slides.table.gz (92/84/fopen): adding to final database dump
0030.036 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_slides_bkp.table.tmpr0.gz (93/84/fopen): adding to final database dump
0030.041 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_slides_bkp.table.gz (94/84/fopen): adding to final database dump
0030.046 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_static_slides.table.tmpr2.gz (95/84/fopen): adding to final database dump
0030.050 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_static_slides.table.gz (96/84/fopen): adding to final database dump
0030.055 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_static_slides_bkp.table.tmpr0.gz (97/84/fopen): adding to final database dump
0030.058 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_revslider_static_slides_bkp.table.gz (98/84/fopen): adding to final database dump
0030.062 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_snippets.table.tmpr6.gz (99/84/fopen): adding to final database dump
0030.069 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_snippets.table.gz (100/84/fopen): adding to final database dump
0030.072 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_events.table.tmpr0.gz (101/84/fopen): adding to final database dump
0030.076 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_events.table.gz (102/84/fopen): adding to final database dump
0030.080 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_exclusions.table.tmpr0.gz (103/84/fopen): adding to final database dump
0030.084 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_exclusions.table.gz (104/84/fopen): adding to final database dump
0030.088 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_historical.table.tmpr0.gz (105/84/fopen): adding to final database dump
0030.092 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_historical.table.gz (106/84/fopen): adding to final database dump
0030.095 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_pages.table.tmpr5850.gz (107/84/fopen): adding to final database dump
0030.101 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_pages.table.gz (108/84/fopen): adding to final database dump
0030.106 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_search.table.tmpr1426.gz (109/84/fopen): adding to final database dump
0030.110 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_search.table.gz (110/84/fopen): adding to final database dump
0030.113 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_useronline.table.tmpr0.gz (111/84/fopen): adding to final database dump
0030.117 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_useronline.table.gz (112/84/fopen): adding to final database dump
0030.121 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_visit.table.tmpr2687.gz (113/84/fopen): adding to final database dump
0030.125 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_visit.table.gz (114/84/fopen): adding to final database dump
0030.128 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_visitor.table.tmpr9056.gz (115/84/fopen): adding to final database dump
0030.141 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_visitor.table.gz (116/84/fopen): adding to final database dump
0030.145 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_visitor_relationships.table.tmpr0.gz (117/84/fopen): adding to final database dump
0030.148 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_statistics_visitor_relationships.table.gz (118/84/fopen): adding to final database dump
0030.152 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_tm_taskmeta.table.tmpr1966.gz (119/84/fopen): adding to final database dump
0030.156 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_tm_taskmeta.table.gz (120/84/fopen): adding to final database dump
0030.160 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_tm_tasks.table.tmpr1966.gz (121/84/fopen): adding to final database dump
0030.166 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_tm_tasks.table.gz (122/84/fopen): adding to final database dump
0030.170 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_ru_ru_en_us.table.tmpr24732.gz (123/84/fopen): adding to final database dump
0030.211 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_ru_ru_en_us.table.gz (124/84/fopen): adding to final database dump
0030.215 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_ru_ru_tr_tr.table.tmpr24760.gz (125/84/fopen): adding to final database dump
0030.247 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_ru_ru_tr_tr.table.gz (126/84/fopen): adding to final database dump
0030.251 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_ru_ru_uk.table.tmpr24576.gz (127/84/fopen): adding to final database dump
0030.284 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_ru_ru_uk.table.gz (128/84/fopen): adding to final database dump
0030.288 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_de_de.table.tmpr3587.gz (129/84/fopen): adding to final database dump
0030.294 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_de_de.table.gz (130/84/fopen): adding to final database dump
0030.298 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_en_us.table.tmpr6501.gz (131/84/fopen): adding to final database dump
0030.309 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_en_us.table.gz (132/84/fopen): adding to final database dump
0030.313 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_ru_ru.table.tmpr6840.gz (133/84/fopen): adding to final database dump
0030.325 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_ru_ru.table.gz (134/84/fopen): adding to final database dump
0030.330 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_uk.table.tmpr6740.gz (135/84/fopen): adding to final database dump
0030.342 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_uk.table.gz (136/84/fopen): adding to final database dump
0030.346 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_zh_cn.table.tmpr11.gz (137/84/fopen): adding to final database dump
0030.351 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_dictionary_tr_tr_zh_cn.table.gz (138/84/fopen): adding to final database dump
0030.354 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_de_de.table.tmpr2279.gz (139/84/fopen): adding to final database dump
0030.430 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_de_de.table.gz (140/84/fopen): adding to final database dump
0030.434 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_en_us.table.tmpr3164.gz (141/84/fopen): adding to final database dump
0030.440 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_en_us.table.gz (142/84/fopen): adding to final database dump
0030.445 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_original_meta.table.tmpr0.gz (143/84/fopen): adding to final database dump
0030.449 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_original_meta.table.gz (144/84/fopen): adding to final database dump
0030.456 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_original_strings.table.tmpr3725.gz (145/84/fopen): adding to final database dump
0030.463 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_original_strings.table.gz (146/84/fopen): adding to final database dump
0030.467 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_ru_ru.table.tmpr3543.gz (147/84/fopen): adding to final database dump
0030.475 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_ru_ru.table.gz (148/84/fopen): adding to final database dump
0030.483 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_tr_tr.table.tmpr3122.gz (149/84/fopen): adding to final database dump
0030.490 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_tr_tr.table.gz (150/84/fopen): adding to final database dump
0030.494 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_uk.table.tmpr2658.gz (151/84/fopen): adding to final database dump
0030.502 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_uk.table.gz (152/84/fopen): adding to final database dump
0030.506 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_zh_cn.table.tmpr0.gz (153/84/fopen): adding to final database dump
0030.509 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_gettext_zh_cn.table.gz (154/84/fopen): adding to final database dump
0030.514 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_original_meta.table.tmpr45014.gz (155/84/fopen): adding to final database dump
0030.664 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_original_meta.table.gz (156/84/fopen): adding to final database dump
0030.681 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_original_strings.table.tmpr31227.gz (157/84/fopen): adding to final database dump
0030.714 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_trp_original_strings.table.gz (158/84/fopen): adding to final database dump
0030.801 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpfm_backup.table.tmpr0.gz (159/84/fopen): adding to final database dump
0030.812 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpfm_backup.table.gz (160/84/fopen): adding to final database dump
0030.898 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpforms_logs.table.tmpr0.gz (161/84/fopen): adding to final database dump
0030.973 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpforms_logs.table.gz (162/84/fopen): adding to final database dump
0030.976 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpforms_payment_meta.table.tmpr0.gz (163/84/fopen): adding to final database dump
0030.980 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpforms_payment_meta.table.gz (164/84/fopen): adding to final database dump
0031.053 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpforms_payments.table.tmpr0.gz (165/84/fopen): adding to final database dump
0031.058 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpforms_payments.table.gz (166/84/fopen): adding to final database dump
0031.061 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpforms_tasks_meta.table.tmpr55.gz (167/84/fopen): adding to final database dump
0031.065 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpforms_tasks_meta.table.gz (168/84/fopen): adding to final database dump
0031.068 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpo_404_detector.table.tmpr0.gz (169/84/fopen): adding to final database dump
0031.072 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db-table-wp_wpo_404_detector.table.gz (170/84/fopen): adding to final database dump
0031.087 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db.gz: finished writing out complete database file (38795.5 KB)
0031.502 (0) Total database tables backed up: 84 (backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db.gz, size: 39726741, sha1: 114e9021c673534134f70f5f106d9056a5af796a, sha256: 3ce0694f164c2a74ea84e437e84bba8cbf6ff4b0c8a23b68ec0e09532865abbb)
0031.554 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes.zip: themes: This file has already been successfully processed
0031.702 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes2.zip: themes: This file has already been successfully processed
0031.705 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes3.zip: themes: This file has already been successfully processed
0031.709 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes4.zip: themes: This file has already been successfully processed
0031.713 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes5.zip: themes: This file has already been successfully processed
0031.717 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-themes6.zip: themes: This file has already been successfully processed
0031.720 (0) backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db.gz: db: This file has not yet been successfully uploaded: will queue
0031.723 (0) Saving backup history. Total backup size: 181.8 MB
0031.746 (0) Requesting upload of the files that have not yet been successfully uploaded (1)
0031.760 (0) Cloud backup selection (1/1): none with instance (1/1) (last)
0031.763 (0) No remote despatch: user chose no remote backup service
0031.766 (0) Recording as successfully uploaded: backup_2025-07-26-2151_Uptrend_Homes_ba1ba9b75417-db.gz
0031.773 (0) Prune old backups from local store: nothing to do, since the user disabled local deletion and we are using local backups
0031.775 (0) Resume backup (ba1ba9b75417, 0): finish run
0031.784 (0) Decremented the semaphore (fd) by 1
0031.790 (0) Semaphore (fd) unlocked
0031.793 (0) There were no errors in the uploads, so the 'resume' event (1) is being unscheduled
0031.804 (0) No email will/can be sent - the user has not configured an email address.
0031.807 (0) The backup succeeded and is now complete
